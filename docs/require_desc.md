# 统一货架连续包月商品展示需求描述文档

## 1. 需求概述

### 1.1 业务背景
基于POI货架场景，为连续包月商品提供专属的展示样式和信息展示方式。连续包月商品作为一种特殊的多次卡商品类型，需要通过差异化的标题、价格、标签和销量展示来突出其连续包月的特点和用户权益。

### 1.2 需求范围
根据统一货架职责范围定义，本次需求涉及货架商品卡片展示相关的以下功能模块：
- **系统内需求**：商品标题展示、副标题展示、价格展示、优惠标签展示、销量展示
- **系统外需求**：TAB快筛召回规则、销量统计计算逻辑、销量扣减业务逻辑（仅作依赖说明）

### 1.3 核心目标
实现连续包月商品在货架中的差异化展示，包括价格多维度展示（¥X/月 「Y期¥Z」 ¥W）、安心练IP标签、特殊优惠标签和销量展示格式，同时保持非连续包月商品的展示逻辑不变。

## 2. 功能需求

### 2.1 商品标题展示

#### 功能描述
连续包月商品的标题展示功能，与普通商品保持一致的标题获取和展示逻辑。

#### 输出字段和过程处理字段
- 输出字段：`filterIdAndProductAreas.productAreas.items.title`
- 过程处理字段：`title.text`（标题文本内容）

#### 业务逻辑
- **实现位置：UnifiedShelfItemTitleVP + 现有TitleOpt实现类**
  - **业务规则详情**：
    - 线上逻辑：从ProductM商品模型中获取商品标题信息，支持多种标题获取策略
    - 本次需求改造：无需特殊改造，连续包月商品复用现有标题展示逻辑
  - **异常处理**：当标题信息缺失时，使用默认标题或商品名称作为降级方案

### 2.2 商品副标题展示

#### 功能描述
连续包月商品在副标题位置新增「安心练IP标签」，同时展示"放心付、安心退、跑路赔"三大服务承诺。

#### 输出字段和过程处理字段
- 输出字段：`filterIdAndProductAreas.productAreas.items.productTags`
- 过程处理字段：
  - `productTags.tags`（副标题标签列表）
  - `productTags.iconTag`（安心练IP标签图标）

#### 业务逻辑
- **实现位置1：UnifiedShelfItemSubTitleVP + UnifiedShelfItemCommonSubTitleOpt**
  - **业务规则详情**：
    - 线上逻辑：现有副标题框架支持多种副标题策略，包括安心练标签逻辑
    - 本次需求改造：针对连续包月商品，启用安心练IP标签展示
      - 触发条件：商品具备配置的安心标签码（`anXinTagCodes`）或安心商品标签码（`anXinProductTagCodes`）
      - 图标展示：通过配置管理安心图标URL
      - 文案展示：支持"放心付、安心退、跑路赔"三个文案，通过`anXinTextList`配置
      - 连接样式：通过`anXinJoinType`设置连接样式（竖线或圆点）
  - **异常处理**：当安心标签配置缺失时，降级为普通副标题展示

### 2.3 商品价格展示

#### 功能描述
连续包月商品的价格采用多维度展示格式：¥X/月 「Y期¥Z」 ¥W，其中X为月均价格，Y为期数，Z为总价，W为门市价。

#### 输出字段和过程处理字段
- 输出字段：
  - `filterIdAndProductAreas.productAreas.items.displaySalePrice`（展示售价）
  - `filterIdAndProductAreas.productAreas.items.salePriceSuffix`（价格后缀）
  - `filterIdAndProductAreas.productAreas.items.marketPrice`（市场价）
- 过程处理字段：
  - `salePrice`（原始销售价格）
  - `salePriceSuffixObject`（价格后缀详细对象）

#### 业务逻辑
- **实现位置1：UnifiedShelfItemDisplaySalePriceVP + 新增ContinuousMonthlyDisplayPriceOpt**
  - **业务规则详情**：
    - 线上逻辑：现有TimesDealSinglePriceOpt处理多次卡单价展示
    - 本次需求改造：新增连续包月价格展示逻辑
      - 商品识别：通过`productM.isTimesDeal() && isContinuousMonthly(productM)`判断
      - 月均价格：直接使用数据层提供的月均价格，展示为"¥X/月"格式
      - 非连续包月：继续使用现有逻辑（TimesDealSinglePriceOpt或DefaultItemDisplaySalePriceOpt）
  - **异常处理**：当月均价格数据异常时，降级为原始销售价格展示

- **实现位置2：UnifiedShelfSalePriceSuffixVP + 新增ContinuousMonthlyPriceSuffixOpt**
  - **业务规则详情**：
    - 线上逻辑：现有ConditionSalePriceSuffixOpt支持根据条件返回价格后缀
    - 本次需求改造：新增连续包月价格后缀逻辑
      - 后缀格式：「Y期¥Z」，其中Y来源于`sys_multi_sale_number`属性，Z为销售价格
      - 期数解析：支持字符串、JSON数组等多种格式的期数数据
      - 格式化：确保后缀格式符合「Y期¥Z」的展示要求
  - **异常处理**：当期数或总价数据缺失时，返回空字符串，不展示后缀

- **实现位置3：UnifiedShelfItemMarketPriceVP + 现有MarketPriceOpt实现类**
  - **业务规则详情**：
    - 线上逻辑：支持多种市场价展示策略，包括条件过滤和降级处理
    - 本次需求改造：无需特殊改造，连续包月商品复用现有市场价展示逻辑
  - **异常处理**：当门市价缺失或为0时，不展示门市价部分，其他价格信息正常展示

### 2.4 优惠标签展示

#### 功能描述
连续包月商品的优惠标签展示采用「Y期¥Z」格式，替代传统的折扣标签展示。

#### 输出字段和过程处理字段
- 输出字段：`filterIdAndProductAreas.productAreas.items.promoTags`
- 过程处理字段：
  - `promoTags.tags.text`（标签文案）
  - `promoTags.tags.backgroundColor`（标签背景色）
  - `promoTags.tags.textColor`（标签文字颜色）

#### 业务逻辑
- **实现位置：UnifiedShelfItemPromoTagVP + UnifiedCommonItemPromoTagOpt**
  - **业务规则详情**：
    - 线上逻辑：现有逻辑支持折扣标签、比价标签、多次卡标签等多种标签类型
    - 本次需求改造：在现有多次卡标签处理基础上，新增连续包月特殊逻辑
      - 标签判断：先判断`isContinuousMonthly(productM)`，如果是连续包月则调用`buildContinuousMonthlyTag`
      - 标签格式：「Y期¥Z」，其中Y为期数，Z为销售价格
      - 标签样式：复用现有标签样式（橙色背景+红色文字）
      - 普通多次卡：如果不是连续包月，继续使用「X次¥Y」格式
  - **异常处理**：当期数或售价数据异常时，返回null，不展示优惠标签

### 2.5 销量信息展示

#### 功能描述
连续包月商品的销量展示采用"年售XX"的特殊格式，区别于普通商品的销量展示。

#### 输出字段和过程处理字段
- 输出字段：`filterIdAndProductAreas.productAreas.items.buttonCarouselMsg`
- 过程处理字段：
  - `buttonCarouselMsg.text.text`（轮播文本内容）
  - `buttonCarouselMsg.type`（轮播类型，1表示销量）

#### 业务逻辑
- **实现位置：UnifiedShelfItemCarouselMsgVP + CommonItemCarouselMsgOpt**
  - **业务规则详情**：
    - 线上逻辑：现有逻辑通过策略模式支持多种轮播信息，包括销量、购买、倒计时等
    - 本次需求改造：无需特殊改造，连续包月商品复用现有销量轮播逻辑
      - 数据获取：通过`productM.getSale().getSaleTag()`获取已格式化的销量文案
      - 文案格式：数据层提供"年售XX"格式的销量文案
      - 样式处理：热门SPU使用红色样式，普通商品使用灰色样式
  - **异常处理**：当销量数据缺失时，不展示销量轮播信息

## 3. 依赖与约束

### 3.1 外部系统依赖
- **数据层服务**：提供连续包月商品的识别属性、月均价格、期数信息等基础数据
- **销量统计服务**：提供"年售XX"格式的销量数据，包含连续包月特殊的统计规则
- **商品属性服务**：提供连续包月标识属性`continuousMonthlySubscription`和安心标签相关属性

### 3.2 数据依赖
- **商品模型（ProductM）**：
  - `isTimesDeal()`：多次卡类型判断
  - `getAttr("continuousMonthlySubscription")`：连续包月标识属性
  - `getAttr("sys_multi_sale_number")`：期数信息
  - `getSale().getSaleTag()`：销量标签文案
- **价格数据**：
  - 月均价格：由数据层计算提供，展示层直接使用
  - 销售价格：用于总价展示和标签生成
  - 市场价格：用于门市价展示

### 3.3 业务约束
- **职责边界**：统一货架接口仅负责展示逻辑，不承担复杂的业务计算（如月均价计算、优惠金额计算）
- **兼容性要求**：非连续包月商品的展示逻辑必须保持与线上完全一致
- **降级策略**：当连续包月特有数据缺失时，必须能够降级到普通商品展示逻辑
- **配置驱动**：安心练IP标签等展示元素通过配置管理，支持灵活调整

## 附加说明
本文档中的Opt是VP的实现类，在实际开发中VP定义展示能力，Opt提供具体的实现逻辑。每个功能模块都包含对应的VP（展示能力）和Opt（实现类）两个部分，它们应该同时进行开发和配置。连续包月商品的特殊展示逻辑主要通过新增专门的Opt实现类来完成，同时保持现有Opt的兼容性。

---
**文档版本**：v5.0
**创建时间**：2024年12月
**适用范围**：统一货架接口连续包月商品展示需求
**文档状态**：已完成需求增强和信息补充 