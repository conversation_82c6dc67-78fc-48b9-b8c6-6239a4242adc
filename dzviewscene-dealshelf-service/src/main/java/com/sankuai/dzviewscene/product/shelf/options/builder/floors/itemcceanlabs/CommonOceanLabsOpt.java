package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.cloud.mos.iam.access.api.util.EnvUtils;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.ability.options.ZdcTagIdFetcherOpt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemOceanLabsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs.utils.OceanLabsUtil;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.NationSubsidyPromoTagStrategy;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/17
 */
@VPointOption(name = "通用-通用商品埋点",
        description = "返回通用的商品埋点",
        code = "CommonOceanLabsOpt",
        isDefault = true)
public class CommonOceanLabsOpt extends ItemOceanLabsVP<CommonOceanLabsOpt.Config> {

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        Map<String, Object> oceanMap = getCommonOcean(param);
        paddingDefaultLab(oceanMap);
        paddingPromoTagLab(activityCxt, oceanMap, param.getProductM());
        if (config.isEnableUGCTagLabs()) {
            paddingUGCTag(oceanMap, param.getDzItemVO(), param.getProductM());
        }
        if (config.isEnableTopDisplayInfoLabs()) {
            addTopDisplayInfo(oceanMap, param.getProductM());
        }
        paddingLabelName(oceanMap, param.getProductM(), param);
        // 竞争圈销量标签
        paddingTradeTag(oceanMap, param.getDzItemVO(), param.getProductM());
        //安心医 for齿科-种植牙补牙
        if (config.enableMedicalAnXinLabs){
            paddingMedicalAnXinType(oceanMap, param.getProductM(), activityCxt,config);
            return JsonCodec.encode(oceanMap);
        }
        // 产品交易类型 for齿科-种植牙
        if (config.isEnableTradeTypeLabs()) {
            paddingTradeType(oceanMap, param.getProductM(), config.getProductId2TradeTypeMap());
            return JsonCodec.encode(oceanMap);
        }
        return JsonCodec.encode(oceanMap);
    }


    private void paddingMedicalAnXinType(Map<String, Object> oceanMap, ProductM productM, ActivityCxt activityCxt, Config config) {
        List<MedicalAnxinConfig> medicalAnxinConfigs = config.getMedicalAnxinConfigs();
        if (CollectionUtils.isEmpty(medicalAnxinConfigs)) {
            return;
        }
        for (MedicalAnxinConfig anxinConfig : medicalAnxinConfigs) {
            Long poiTagId = EnvUtils.isOnline() ? anxinConfig.poiTagIdForOnline : anxinConfig.poiTagIdForTest;
            if (hasGuaranteeTagCode(productM, anxinConfig.productTagName) && hasDisplayPOITag(activityCxt, poiTagId)) {
                oceanMap.put("type", "安心医");
                break;
            }
        }
    }

    private boolean hasDisplayPOITag(ActivityCxt context,Long tagId) {
        List<Long> poiTagIds = context.getParam(ZdcTagIdFetcherOpt.CODE);
        return CollectionUtils.isNotEmpty(poiTagIds) && poiTagIds.contains(tagId);
    }

    private boolean hasGuaranteeTagCode(ProductM productM, String guaranteeTagName) {
        if (!"3".equals(productM.getAttr("guaranteeType"))) {
            return false;
        }
        return StringUtils.isNotEmpty(guaranteeTagName) && guaranteeTagName.equals(productM.getAttr("medicalSafeTreatTagName"));
    }
    
    private void paddingTradeType(Map<String, Object> oceanMap, ProductM productM, Map<String, String> productId2TradeTypeMap) {
        List<String> tagIdList = Optional.ofNullable(productM.getProductTagList()).orElse(new ArrayList<>()).stream()
                .map(TagM::getId).collect(Collectors.toList());
        String tradeType = productId2TradeTypeMap.entrySet().stream()
                .filter(entry -> tagIdList.contains(entry.getKey()))
                .map(Map.Entry::getValue).findFirst().orElse("团购");
        oceanMap.put("type", tradeType);
    }

    private void addTopDisplayInfo(Map<String, Object> oceanMap, ProductM productM) {
        //是否堆头
        boolean isTopDisplay = ProductMAttrUtils.isTopDisplayProduct(productM);
        //0-普通团购，1-堆头
        oceanMap.put("module_show_type", isTopDisplay ? "1" : "0");
        oceanMap.put("type", ProductTypeEnum.getByType(productM.getProductType()).getDesc());
        oceanMap.put("module_name", "团购货架");
    }

    private void paddingLabelName(Map<String, Object> source, ProductM productM, Param param) {
        // 填充买贵必赔角标
        OceanLabsUtil.paddingLabelNameByPriceGuarantee(source, productM, param);
    }

    private void paddingDefaultLab(Map<String, Object> source) {
        source.put("activity_status", -999);
        source.put("alliance", -999);
        source.put("click_type", -999);
        source.put("customize_array", -999);
        source.put("deal_type", -999);
        source.put("label_content", -999);
        source.put("label_name", -999);
        source.put("select_name", -999);
        source.put("view_type", -999);
    }

    private void paddingTradeTag(Map<String, Object> source, DzItemVO dzItemVO, ProductM productM) {
        List<DzTagVO> priceAboveTags = dzItemVO.getPriceAboveTags();
        String tradeRateTag = PriceAboveTagsUtils.getTradeRateTag(productM);
        if (CollectionUtils.isEmpty(priceAboveTags) || StringUtils.isBlank(tradeRateTag)) {
            source.put("trade_rate_tag", -999);
            return;
        }
        String priceAboveTag = priceAboveTags.get(0).getText();
        source.put("trade_rate_tag", StringUtils.equals(priceAboveTag, tradeRateTag) ? 0 : -999);
    }

    /**
     * 第三行UGC标签
     *
     * @param source
     * @param dzItemVO
     * @param productM
     */
    private void paddingUGCTag(Map<String, Object> source, DzItemVO dzItemVO, ProductM productM) {
        List<DzTagVO> priceAboveTags = dzItemVO.getPriceAboveTags();
        List<String> recommendTags = PriceAboveTagsUtils.getRecommendTag(productM);
        if (CollectionUtils.isEmpty(priceAboveTags) || CollectionUtils.isEmpty(recommendTags)) {
            source.put("UGC_tag", -999);
            return;
        }
        String firstPriceAboveTag = priceAboveTags.get(0).getText();
        String firstRecommendTag = recommendTags.get(0);
        source.put("UGC_tag", StringUtils.isNotBlank(firstPriceAboveTag) && StringUtils.isNotBlank(firstRecommendTag)
                && firstPriceAboveTag.contains(firstRecommendTag) ? 0 : -999);
    }

    /**
     * 填充优惠标签相关打点
     *
     * @param activityCxt
     * @param productM
     */
    private void paddingPromoTagLab(ActivityCxt activityCxt, Map<String, Object> source, ProductM productM) {
        // 没有优惠信息，就没有优惠标签，返回默认
        if (CollectionUtils.isEmpty(productM.getPromoPrices())) {
            source.put("promotion_array", -999);
            source.put("promotion_title", -999);
            return;
        }
        source.put("promotion_array", productM.getPromoPrices().get(0).getPromoItemList());
        if (PriceUtils.isSupportDp4NationalSubsidy(ParamsUtil.getIntSafely(activityCxt, ShelfActivityConstants.Params.platform)) && PriceUtils.hasNationalSubsidy(productM)) {
            source.put("promotion_title", NationSubsidyPromoTagStrategy.buildNationalSubsidyText(productM));
            return;
        }
        // 判断是否有预售
        if (MassageOceanLabsOpt.preSalePromoTagType.contains(productM.getPromoPrices().get(0).getPromoTagType())) {
            source.put("promotion_title", getPromoTagDesc(productM.getPromoPrices().get(0).getPromoTagType()) + productM.getPromoPrices().get(0).getPromoTag());
        } else if (RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(productM)) {
            if (MassageOceanLabsOpt.newUserPromoTagType.contains(productM.getPromoPrices().get(0).getPromoTagType())) {
                source.put("promotion_title", "秒杀+新客" + productM.getPromoPrices().get(0).getPromoTag() + productM.getPromoPrices().get(0).getPromoTag());
            } else {
                source.put("promotion_title", "秒杀" + productM.getPromoPrices().get(0).getPromoTag() + productM.getPromoPrices().get(0).getPromoTag());
            }
        } else {
            if (Objects.nonNull(productM.getPromoPrices().get(0)) && Objects.nonNull(productM.getPromoPrices().get(0).getPromoTagType())) {
                String title = getPromoTagDesc(productM.getPromoPrices().get(0).getPromoTagType());
                if (Strings.isNotBlank(title)) {
                    source.put("promotion_title", title + productM.getPromoPrices().get(0).getPromoTag());
                    return;
                }
            }
            source.put("promotion_array", -999);
            source.put("promotion_title", -999);
        }
    }

    private String getPromoTagDesc(int code) {
        if (code == PromoTagTypeEnum.Default.getCode()) {
            return null;
        } else if (code == PromoTagTypeEnum.Other.getCode()) {
            return "特惠促销";
        }
        for (PromoTagTypeEnum tagTypeEnum : PromoTagTypeEnum.values()) {
            if (tagTypeEnum.getCode() == code) {
                return tagTypeEnum.getDesc();
            }
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 是否启用「UGC短语」打点
         */
        private boolean enableUGCTagLabs = false;

        /**
         * 是否启用「堆头商品」打点
         */
        private boolean enableTopDisplayInfoLabs = false;

        /**
         * 是否启用「交易类型」打点
         */
        private boolean enableTradeTypeLabs = false;

        /*
        * 是否启用「安心医」打点
        * */
        private boolean enableMedicalAnXinLabs = false;

        private Map<String, String> productId2TradeTypeMap = new HashMap<>();

        private List<MedicalAnxinConfig> medicalAnxinConfigs ;
    }

    @Data
    public static class MedicalAnxinConfig {
        private String productTagName;
        private Long poiTagIdForTest;
        private Long poiTagIdForOnline;
    }
}
