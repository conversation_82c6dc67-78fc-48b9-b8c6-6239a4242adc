package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.NationSubsidyPromoTagStrategy;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import org.springframework.stereotype.Component;

@Component
public class NationSubsidyPromoTagBuildStrategy extends AbstractPriceBottomPromoTagBuildStrategy {
    @Override
    public DzTagVO buildTag(PriceBottomTagBuildReq req) {
        // 点评侧，或者没有国补则返回空
        if (!PriceUtils.isSupportDp4NationalSubsidy(req.getPlatform()) || !PriceUtils.hasNationalSubsidy(req.getProductM())) {
            return null;
        }
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setTextColor(ColorUtils.colorFFFFFF);
        dzTagVO.setBackground(ColorUtils.color00A72D);
        dzTagVO.setText(NationSubsidyPromoTagStrategy.buildNationalSubsidyText(req.getProductM()));
        dzTagVO.setMultiText(Lists.newArrayList(NationSubsidyPromoTagStrategy.buildNationalSubsidyText(req.getProductM())));
        dzTagVO.setBorderRadius(3);
        return dzTagVO;
    }

    @Override
    public String getName() {
        return "国补";
    }
}
