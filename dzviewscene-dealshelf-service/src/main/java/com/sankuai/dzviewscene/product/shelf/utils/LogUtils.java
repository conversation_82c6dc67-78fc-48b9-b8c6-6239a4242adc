package com.sankuai.dzviewscene.product.shelf.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterBtnIdAndProAreasVO;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.Function;

@Slf4j
public class LogUtils {

    public static String DEAL_SHELF_KEY_FIELD = "deal_shelf_key_field";

    public static String TEMPLATE_FLOW_KEY = "shelf_template_flow";

    private static final Map<String, Function<ActivityContextRequest, Object>> PARAM_GETTERS = new HashMap<>();

    static {
        PARAM_GETTERS.put("shopId", ActivityContextRequest::getShopId);
        PARAM_GETTERS.put("shopUuid", ActivityContextRequest::getShopUuid);
        PARAM_GETTERS.put("moduleName", ActivityContextRequest::getModuleName);
        PARAM_GETTERS.put("shopType", ActivityContextRequest::getShopType);
        PARAM_GETTERS.put("useType", ActivityContextRequest::getUseType);
        PARAM_GETTERS.put("shopMainCategoryId", ActivityContextRequest::getShopMainCategoryId);
        PARAM_GETTERS.put("shareCode", ActivityContextRequest::getShareCode);
        PARAM_GETTERS.put("productVersion", ActivityContextRequest::getProductVersion);
        PARAM_GETTERS.put("productItemId", ActivityContextRequest::getProductItemId);
        PARAM_GETTERS.put("purchaseDate", ActivityContextRequest::getPurchaseDate);
        PARAM_GETTERS.put("userId", ActivityContextRequest::getUserId);
        PARAM_GETTERS.put("summaryProductIds", ActivityContextRequest::getSummaryProductIds);
        PARAM_GETTERS.put("topProductIds", ActivityContextRequest::getTopProductIds);
        PARAM_GETTERS.put("anchorGoodId", ActivityContextRequest::getAnchorGoodId);
        PARAM_GETTERS.put("recommendinfo", ActivityContextRequest::getRecommendinfo);
        PARAM_GETTERS.put("refreshTag", ActivityContextRequest::getRefreshTag);
        PARAM_GETTERS.put("wttRegionId", ActivityContextRequest::getWttRegionId);
        PARAM_GETTERS.put("spaceKey", ActivityContextRequest::getSpaceKey);
        PARAM_GETTERS.put("deviceHeight", ActivityContextRequest::getDeviceHeight);
    }

    public static void logMetric(List<FilterBtnIdAndProAreasVO> filterBtnIdAndProAreasVOS, String scene, long shopId, int platform) {
        if (CollectionUtils.isEmpty(filterBtnIdAndProAreasVOS)) {
            return;
        }
        filterBtnIdAndProAreasVOS.stream().filter(item -> CollectionUtils.isNotEmpty(item.getProductAreas()))
                .forEach(item -> {
                    item.getProductAreas().stream().filter(productArea -> Objects.nonNull(productArea) && Objects.nonNull(productArea.getItemArea())
                                    && CollectionUtils.isNotEmpty(productArea.getItemArea().getProductItems()))
                            .forEach(productArea -> {
                                productArea.getItemArea().getProductItems().stream().filter(dzItemVO -> Objects.nonNull(dzItemVO))
                                        .forEach(dzItemVO -> logMetric(dzItemVO, scene, shopId, platform));
                            });
                });
    }

    public static void logMetric(DzItemVO dzItemVO, String scene, long shopId, int platform) {
        logMetricForTitle(dzItemVO, scene, shopId, platform);
        logMetricForSalePrice(dzItemVO, scene, shopId, platform);
        logMetricForPic(dzItemVO, scene, shopId, platform);
    }

    public static void logMetricForTitle(DzItemVO dzItemVO, String scene, long shopId, int platform) {
        if (emptyTitle(dzItemVO)) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("scene", scene);
            tagMap.put("platform", String.valueOf(platform));
            tagMap.put("field", "title");
            Cat.logMetricForCount(DEAL_SHELF_KEY_FIELD, tagMap);
            logEmptyContext(dzItemVO, scene, shopId, platform, "title");
        }
    }

    public static void logMetricForSalePrice(DzItemVO dzItemVO, String scene, long shopId, int platform) {
        if (emptySalePrice(dzItemVO)) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("scene", scene);
            tagMap.put("platform", String.valueOf(platform));
            tagMap.put("field", "salePrice");
            Cat.logMetricForCount(DEAL_SHELF_KEY_FIELD, tagMap);
            logEmptyContext(dzItemVO, scene, shopId, platform, "salePrice");
        }
    }

    public static void logMetricForPic(DzItemVO dzItemVO, String scene, long shopId, int platform) {
        if (emptyPic(dzItemVO)) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("scene", scene);
            tagMap.put("platform", String.valueOf(platform));
            tagMap.put("field", "pic");
            Cat.logMetricForCount(DEAL_SHELF_KEY_FIELD, tagMap);
            logEmptyContext(dzItemVO, scene, shopId, platform, "pic");
        }
    }

    private static void logEmptyContext(DzItemVO dzItemVO, String scene, long shopId, int platform, String field) {
        int logFlow = Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0);
        if (logFlow <= 0) {
            return;
        }
        if (System.currentTimeMillis() % logFlow == 0) {
            log.info(XMDLogFormat.build()
                    .putTag("method", "logEmptyContext")
                    .message(String.format("deal shelf field empty, dealId:%s, field:%s, shopId:%s, platform:%s, scene:%s",
                            dzItemVO.getItemIdL(), field, shopId, platform, scene)));
        }
    }

    public static boolean emptySalePrice(DzItemVO dzItemVO) {
        return StringUtils.isEmpty(dzItemVO.getSalePrice());
    }

    public static boolean emptyPic(DzItemVO dzItemVO) {
        return dzItemVO.getPic() == null || dzItemVO.getPic().getPic() == null || StringUtils.isEmpty(dzItemVO.getPic().getPic().getPicUrl());
    }

    public static boolean emptyTitle(DzItemVO dzItemVO) {
        return StringUtils.isEmpty(dzItemVO.getTitle());
    }


    public static boolean recordKeyMsg(long userId, String keyDesc, Object keyMsg) {
        //用户白名单
        try {
            List<Long> whiteUserId = Lion.getList("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.log.userId", Long.class);
            if (CollectionUtils.isNotEmpty(whiteUserId) && whiteUserId.contains(userId)) {
                log.warn(String.format("userId:%d,keyDesc:%s,keyMsg:%s", userId, keyDesc, JSON.toJSONString(keyMsg)));
                return true;
            }
            if (LogControl.debugOpen()) {
                log.warn(String.format("userId:%d,keyDesc:%s,keyMsg:%s", userId, keyDesc, JSON.toJSONString(keyMsg)));
                return true;
            }
        } catch (Throwable e) {
            log.error("recordKeyMsg.error");
            return false;
        }
        return false;
    }

    public static boolean recordKeyMsg(String keyDesc, Object keyMsg) {
        log.warn(String.format("keyDesc:%s,keyMsg:%s", keyDesc, JSON.toJSONString(keyMsg)));
        return true;
    }

    public static void markTemplateFlow() {
        Tracer.putContext(TEMPLATE_FLOW_KEY, "1");
    }

    public static void clearTemplateFlow() {
        Tracer.clearContext(TEMPLATE_FLOW_KEY);
    }

    public static void logTemplateFlow(ActivityContextRequest request, DzShelfResponseVO dzShelfResponseVO) {
        try {
            reportShowType(dzShelfResponseVO);
            if (!"1".equals(Tracer.getContext(TEMPLATE_FLOW_KEY))) {
                return;
            }
            int logFlow = Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.template.log.flow", 0);
            if (logFlow <= 0) {
                return;
            }
            if (System.currentTimeMillis() % logFlow == 0) {
                log.warn(String.format("TemplateFlow firstLoad requestSceneCode:%s,responseSceneCode:%s,hasProduct:%s,shopId:%s,platform:%s,userId:%s,client:%s,version:%s,shelfVersion:%s",
                        request.getSceneCode(), dzShelfResponseVO != null && dzShelfResponseVO.getShelfComponent() != null ? dzShelfResponseVO.getShelfComponent().getSceneCode() : "",
                        !ModelUtils.hasNoProducts(dzShelfResponseVO),
                        request.getShopId(), request.getPlatform(), request.getUserId(), request.getClient(), request.getVersion(), request.getShelfVersion()));
            }
        } catch (Throwable e) {
            log.error("logTemplateFlow.error", e);
        }
    }

    public static void logTemplateFlow(ActivityContextRequest request, FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO) {
        try {
            if (!"1".equals(Tracer.getContext(TEMPLATE_FLOW_KEY))) {
                return;
            }
            int logFlow = Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.template.log.flow", 0);
            if (logFlow <= 0) {
                return;
            }
            if (System.currentTimeMillis() % logFlow == 0) {
                log.warn(String.format("TemplateFlow tab requestSceneCode:%s,responseSceneCode:%s,hasProduct:%s,shopId:%s,platform:%s,userId:%s,client:%s,version:%s,shelfVersion:%s",
                        request.getSceneCode(), filterBtnIdAndProAreasVO != null ? filterBtnIdAndProAreasVO.getSceneCode() : "",
                        ModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO),
                        request.getShopId(), request.getPlatform(), request.getUserId(), request.getClient(), request.getVersion(), request.getShelfVersion()));
            }
        } catch (Throwable e) {
            log.error("logTemplateFlow.error", e);
        }
    }

    private static void reportShowType(DzShelfResponseVO dzShelfResponseVO) {
        if(dzShelfResponseVO == null || dzShelfResponseVO.getShelfComponent() == null){
            return;
        }
        Cat.logEvent("DealShelfShowType", String.valueOf(dzShelfResponseVO.getShelfComponent().getShowType()));
    }

    public static void logRequestParameters(ActivityContextRequest request, String sceneCode, String interfaceName) {
        try {
            if (!sceneCodeNeededLog(sceneCode)) {
                return;
            }
            List<String> validParamNames = getValidParameters(request);
            if (CollectionUtils.isNotEmpty(validParamNames)) {
                validParamNames.forEach(paramName -> Cat.logEvent("ValidParameters-" + sceneCode, paramName + "-" + interfaceName));
            }
        } catch (Throwable e) {
            log.error("logRequestParameters.error", e);
        }
    }

    private static boolean sceneCodeNeededLog(String sceneCode) {
        List<String> logSceneCodes = Lion.getList(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.log.sceneCode", String.class, Lists.newArrayList());
        return logSceneCodes.contains(sceneCode);
    }

    private static List<String> getValidParameters(ActivityContextRequest request) {
        List<String> validParams = new ArrayList<>();
        List<String> paramNames = Lion.getList(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.log.paramNames", String.class, Lists.newArrayList());
        for (String paramName : paramNames) {
            Function<ActivityContextRequest, Object> getter = PARAM_GETTERS.get(paramName);
            if (getter != null) {
                Object value = getter.apply(request);
                if (isValidValue(value)) {
                    validParams.add(paramName);
                }
            }
        }
        return validParams;
    }

    private static boolean isValidValue(Object value) {
        if (value == null) {
            return false;
        }
        if (value instanceof String) {
            return StringUtils.isNotEmpty((String) value);
        }
        if (value instanceof Number) {
            return !value.equals(0) && !value.equals(0.0) && !value.equals(0L);
        }
        return true;
    }
}
