package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SPU聚合标题处理
 */
@Component
@Slf4j
public class MassageTitleProcessHandler implements AggregatePostHandler {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.spu.shelf.black.shop", defaultValue = "[]")
    public static List<Long> BLACK_SHOP_IDS = Lists.newArrayList();

    /**
     * 斗槲实验白名单实验号
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.spu.title.douhu.exp.whitelist", defaultValue = "EXP2025030700001_g,EXP2025030700002_g")
    public static String douhuExpWhitelist;

    /**
     * 商品属性key：sys_productName_group
     */
    private static final String PRODUCT_NAME_GROUP_KEY = "productName_group";

    private static final Pattern TITLE_SUFFIX_PATTERN = Pattern.compile("(.*?)｜\\d+分钟[\\u4e00-\\u9fa5]*(?:足疗|按摩|推拿|精油SPA|淋巴净排|艾灸（手工悬灸）|艾灸（盒灸）|艾灸（仪器灸）|艾灸（铺灸）|正骨|推拿（含正骨）|采耳)$");

    @Override
    public String getName() {
        return MassageTitleProcessHandler.class.getSimpleName();
    }

    @Override
    public ShelfItemVO process(ActivityCxt activityCxt, AggregateHandlerContext context) {
        ShelfItemVO shelfItemVO = context.getShelfItemVO();
        if(!ProductHierarchyUtils.isSpuNode(shelfItemVO) || CollectionUtils.isEmpty(shelfItemVO.getSubItems())){
            return shelfItemVO;
        }

        // 判断是否命中斗槲实验
        if (isInDouhuExperiment(activityCxt)) {
            // 命中实验，使用新逻辑处理标题
            processWithNewLogic(activityCxt, context, shelfItemVO);
        } else {
            // 未命中实验，使用原逻辑
            if(shelfItemVO.getSubItems().size() == 1){
                ShelfItemVO childItem = shelfItemVO.getSubItems().get(0);
                //标题拼接SPU
                if (!hitBlackShop(activityCxt)) {
                    List<StyleTextModel> title = Lists.newArrayList();
                    if(CollectionUtils.isNotEmpty(shelfItemVO.getTitle())) {
                        title.addAll(shelfItemVO.getTitle());
                        title.add(buildText("｜"));
                    }
                    title.addAll(parseOriginTitle(activityCxt, childItem.getTitle(), context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId()))));
                    childItem.setTitle(title);
                }
            }else{
                shelfItemVO.getSubItems().forEach(childItem -> {
                    childItem.setTitle(parseOriginTitle(activityCxt, childItem.getTitle(), context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId()))));
                });
            }
        }
        return shelfItemVO;
    }

    private List<StyleTextModel> parseOriginTitle(ActivityCxt activityCxt, List<StyleTextModel> title, ProductM productM) {
        if(productM == null || StringUtils.isEmpty(productM.getTitle())) {
            return title;
        }
        //标题替换原始标题
        try {
            Matcher matcher = TITLE_SUFFIX_PATTERN.matcher(productM.getTitle());
            if (!matcher.find()) {
                return title;
            }
            String originTitle =  matcher.group(1);
            if (StringUtils.isNotEmpty(originTitle)) {
                return UnifiedShelfItemTitleVP.build4TryHighlightText(activityCxt, originTitle, false);
            }
        } catch (Exception e) {
            //do nothing
        }
        return title;
    }

    private boolean hitBlackShop(ActivityCxt context) {
        long dpShopId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.dpPoiIdL);
        if (CollectionUtils.isNotEmpty(BLACK_SHOP_IDS) && BLACK_SHOP_IDS.contains(dpShopId)) {
            return true;
        }
        //配置-1，全量下掉
        if (CollectionUtils.isNotEmpty(BLACK_SHOP_IDS) && BLACK_SHOP_IDS.contains(-1L)) {
            return true;
        }
        return false;
    }

    private StyleTextModel buildText(String text) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(text);
        styleTextModel.setStyle(TextStyleEnum.TEXT_GRAY.getType());
        return styleTextModel;
    }

    /**
     * 判断是否命中斗槲实验
     *
     * @param activityCxt 活动上下文
     * @return 是否命中
     */
    private boolean isInDouhuExperiment(ActivityCxt activityCxt) {
        if (activityCxt == null) {
            return false;
        }

        List<String> whitelistExps = Lists.newArrayList();
        if (StringUtils.isNotEmpty(douhuExpWhitelist)) {
            whitelistExps = Lists.newArrayList(douhuExpWhitelist.split(","));
        }

        if (CollectionUtils.isEmpty(whitelistExps)) {
            return false;
        }

        List<DouHuM> douHuMList = activityCxt.getParam(ShelfActivityConstants.Params.douHus);
        if (CollectionUtils.isEmpty(douHuMList)) {
            return false;
        }

        // 判断是否命中白名单实验
        return DouHuUtils.hitAnySk(douHuMList, whitelistExps);
    }

    /**
     * 使用新逻辑处理标题
     *
     * @param activityCxt 活动上下文
     * @param context 聚合处理上下文
     * @param shelfItemVO 货架项VO
     */
    private void processWithNewLogic(ActivityCxt activityCxt, AggregateHandlerContext context, ShelfItemVO shelfItemVO) {
        // 判断是单deal还是多deal
        boolean isSingleDeal = shelfItemVO.getSubItems().size() == 1;

        if (isSingleDeal) {
            // 单deal处理逻辑
            processSingleDealTitle(activityCxt, context, shelfItemVO);
        } else {
            // 多deal处理逻辑
            processMultiDealTitle(activityCxt, context, shelfItemVO);
        }
    }

    /**
     * 处理单deal SPU标题
     *
     * @param activityCxt 活动上下文
     * @param context 聚合处理上下文
     * @param shelfItemVO 货架项VO
     */
    private void processSingleDealTitle(ActivityCxt activityCxt, AggregateHandlerContext context, ShelfItemVO shelfItemVO) {
        ShelfItemVO childItem = shelfItemVO.getSubItems().get(0);
        ProductM productM = context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId()));

        if (productM == null) {
            return;
        }
        Matcher matcher = TITLE_SUFFIX_PATTERN.matcher(productM.getTitle());
        if (!matcher.find()) {
            return;
        }
        if (!hitBlackShop(activityCxt)) {
            List<StyleTextModel> title = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(shelfItemVO.getTitle())) {
                title.addAll(shelfItemVO.getTitle());
                title.add(buildText("｜"));
            }
            title.addAll(parseOriginTitle(activityCxt, childItem.getTitle(), context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId()))));
            childItem.setTitle(title);
        }
    }

    /**
     * 处理多deal SPU标题
     *
     * @param activityCxt 活动上下文
     * @param context 聚合处理上下文
     * @param shelfItemVO 货架项VO
     */
    private void processMultiDealTitle(ActivityCxt activityCxt, AggregateHandlerContext context, ShelfItemVO shelfItemVO) {
        // 获取SPU标题
        String spuTitle = "";
        if (CollectionUtils.isNotEmpty(shelfItemVO.getTitle())) {
            spuTitle = shelfItemVO.getTitle().stream()
                    .filter(styleText -> styleText != null && StringUtils.isNotEmpty(styleText.getText()))
                    .map(StyleTextModel::getText)
                    .reduce("", String::concat);
        }

        // 处理每个子项的标题
        for (ShelfItemVO childItem : shelfItemVO.getSubItems()) {
            ProductM productM = context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId()));
            if (productM == null) {
                continue;
            }

            // 获取商品名称组信息
            ProductNameGroup productNameGroup = getProductNameGroup(productM);
            if (productNameGroup == null) {
                //保持原逻辑
                childItem.setTitle(parseOriginTitle(activityCxt, childItem.getTitle(), context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId()))));
                continue;
            }

            // 根据团单类型处理标题
            List<StyleTextModel> title;
            if (isFullCustomTitle(productNameGroup)) {
                // 1）全自定义团单：展示原始全自定义团单标题
                title = UnifiedShelfItemTitleVP.build4TryHighlightText(activityCxt, productNameGroup.getProductName_value(), false);
            } else if (isOldStandardSuffixTitle(productNameGroup)) {
                // 2）旧标准化拼接字段后缀团单：不展示拼接字段，展示团单自定义部分
                title = UnifiedShelfItemTitleVP.build4TryHighlightText(activityCxt, productNameGroup.getProductName_value(), false);
            } else if (isNewStandardPrefixTitle(productNameGroup)) {
                // 3）新标准化拼接字段前缀团单：不展示拼接字段中与SPU标题重叠部分，展示团单自定义部分+剩余拼接部分
                String prefix = productNameGroup.getProductName_prefix();
                String value = productNameGroup.getProductName_value();
                String connect = StringUtils.isEmpty(productNameGroup.getProductName_connect()) ? "|" : productNameGroup.getProductName_connect();

                // 如果SPU标题与前缀重叠，则不展示前缀
                if (StringUtils.isNotEmpty(spuTitle) && StringUtils.isNotEmpty(prefix) &&
                    (spuTitle.contains(prefix))) {
                    title = UnifiedShelfItemTitleVP.build4TryHighlightText(activityCxt, value, false);
                } else {
                    // 否则展示前缀+自定义部分
                    title = UnifiedShelfItemTitleVP.build4TryHighlightText(activityCxt, prefix + connect + value, false);
                }
            } else {
                // 保持原逻辑
                title = parseOriginTitle(activityCxt, childItem.getTitle(), context.getProductMMap().get(ProductHierarchyNodeM.identityKey(childItem.getItemType(), childItem.getItemId())));
            }

            childItem.setTitle(title);
        }
    }

    /**
     * 判断是否为全自定义团单标题
     *
     * @param productNameGroup 商品名称组信息
     * @return 是否为全自定义团单标题
     */
    private boolean isFullCustomTitle(ProductNameGroup productNameGroup) {
        return StringUtils.isNotEmpty(productNameGroup.getProductName_value()) &&
               StringUtils.isEmpty(productNameGroup.getProductName_prefix()) &&
               StringUtils.isEmpty(productNameGroup.getProductName_suffix());
    }

    /**
     * 判断是否为旧标准化拼接字段后缀团单标题
     *
     * @param productNameGroup 商品名称组信息
     * @return 是否为旧标准化拼接字段后缀团单标题
     */
    private boolean isOldStandardSuffixTitle(ProductNameGroup productNameGroup) {
        return StringUtils.isNotEmpty(productNameGroup.getProductName_value()) &&
               StringUtils.isNotEmpty(productNameGroup.getProductName_suffix()) &&
               StringUtils.isEmpty(productNameGroup.getProductName_prefix());
    }

    /**
     * 判断是否为新标准化拼接字段前缀团单标题
     *
     * @param productNameGroup 商品名称组信息
     * @return 是否为新标准化拼接字段前缀团单标题
     */
    private boolean isNewStandardPrefixTitle(ProductNameGroup productNameGroup) {
        return StringUtils.isNotEmpty(productNameGroup.getProductName_value()) &&
               StringUtils.isNotEmpty(productNameGroup.getProductName_prefix()) &&
               StringUtils.isEmpty(productNameGroup.getProductName_suffix());
    }

    /**
     * 获取商品名称组信息
     *
     * @param productM 商品信息
     * @return 商品名称组信息
     */
    private ProductNameGroup getProductNameGroup(ProductM productM) {
        String productNameGroupJson = productM.getAttr(PRODUCT_NAME_GROUP_KEY);
        if (StringUtils.isEmpty(productNameGroupJson)) {
            return null;
        }
        return JSON.parseObject(productNameGroupJson, ProductNameGroup.class);
    }

    /**
     * 商品名称组信息
     */
    @Data
    private static class ProductNameGroup {
        /**
         * 前缀
         */
        private String productName_prefix;

        /**
         * 自定义部分
         */
        private String productName_value;

        /**
         * 后缀
         */
        private String productName_suffix;

        private String productName_connect;
    }
}
