package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemmarketprice;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemMarketPriceVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@VPointOption(name = "市场价-根据不同条件返回",
        description = "根据不同条件返回市场价",
        code = "ConditionMarketPriceOpt")
public class ConditionMarketPriceOpt extends UnifiedShelfItemMarketPriceVP<ConditionMarketPriceOpt.Config> {

    private static final Map<String, MarketPriceBuilder> builderMap = Maps.newHashMap();

    static {
        builderMap.put(MarketPriceType.NATIONAL_SUBSIDY.getCode(), getNationalSubsidyDesc());
    }

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getTypes())) {
            return param.getProductM().getMarketPrice();
        }
        for (String type : config.getTypes()) {
            MarketPriceBuilder marketPriceBuilder = builderMap.get(type);
            if (Objects.isNull(marketPriceBuilder)) {
                continue;
            }
            return marketPriceBuilder.build(context, param);
        }
        return param.getProductM().getMarketPrice();
    }

    private static MarketPriceBuilder getNationalSubsidyDesc() {
        return (context, param) -> {
            if (PriceUtils.isSupportDp4NationalSubsidy(ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform))
                    && PriceUtils.hasNationalSubsidy(param.getProductM())) {
                return PriceUtils.getNationSubsidyMarketPriceTag(param.getProductM());
            }
            return param.getProductM().getMarketPrice();
        };
    }

    @FunctionalInterface
    public interface MarketPriceBuilder {
        String build(ActivityCxt context, Param param);
    }

    public enum MarketPriceType {
        NATIONAL_SUBSIDY("nationalSubsidy", "国补市场价")
        ;

        @Getter
        private String code;
        private String desc;

        MarketPriceType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 市场价类型，按顺序配置优先级，配置值见{@link MarketPriceType}枚举
         */
        private List<String> types;
    }
}