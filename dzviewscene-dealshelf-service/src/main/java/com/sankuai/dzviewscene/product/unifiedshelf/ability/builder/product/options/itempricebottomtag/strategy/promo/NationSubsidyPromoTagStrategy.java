package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class NationSubsidyPromoTagStrategy extends AbstractPriceBottomPromoTagBuildStrategy {
    @Override
    public ShelfTagVO buildTag(PriceBottomTagBuildReq req) {
        if (!PriceUtils.isSupportDp4NationalSubsidy(req.getPlatform()) || !PriceUtils.hasNationalSubsidy(req.getProductM())) {
            return null;
        }
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setText(buildMultiText(1, buildNationalSubsidyText(req.getProductM()),
                Lists.newArrayList(buildNationalSubsidyText(req.getProductM())), ColorUtils.colorFFFFFF, ColorUtils.color00A72D));
        return shelfTagVO;
    }

    public static String buildNationalSubsidyText(ProductM productM) {
        String nationSubsidyPriceTag = PriceUtils.getNationSubsidyPriceTag(productM);
        return StringUtils.isNotBlank(nationSubsidyPriceTag) ? "国补价约¥"+nationSubsidyPriceTag : StringUtils.EMPTY;
    }

    @Override
    public String getName() {
        return "国补";
    }
}
