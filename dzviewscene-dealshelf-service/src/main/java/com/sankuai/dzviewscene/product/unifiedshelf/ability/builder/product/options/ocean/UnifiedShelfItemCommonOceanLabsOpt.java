package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ShopUserProfileUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@VPointOption(name = "通用-通用商品埋点",
        description = "返回通用的商品埋点",
        code = "UnifiedShelfItemCommonOceanLabsOpt",
        isDefault = true)
public class UnifiedShelfItemCommonOceanLabsOpt extends UnifiedShelfItemOceanLabsVP<UnifiedShelfItemCommonOceanLabsOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM())){
            return buildSpuLabs(context, param, config);
        }
        if(ProductHierarchyUtils.isSkuNode(param.getNodeM())){
            return buildSkuLabs(context, param, config);
        }
        return buildProductLabs(context, param, config);
    }

    private String buildSpuLabs(ActivityCxt context, Param param, Config config) {
        Map<String, Object> oceanMap = getSpuOcean(param);
        return JsonCodec.encode(oceanMap);
    }

    private String buildSkuLabs(ActivityCxt context, Param param, Config config) {
        Map<String, Object> oceanMap = getSkuOcean(context, param);
        return JsonCodec.encode(oceanMap);
    }

    private String buildProductLabs(ActivityCxt context, Param param, Config config){
        Map<String, Object> oceanMap = getCommonOcean(context, param);
        if (config.isEnableTopDisplayInfoLabs()) {
            paddingTopDisplayInfo(oceanMap, param.getProductM());
        }
        if (config.isEnableCardLabs()) {
            paddingCardLab(oceanMap, param.getCardM());
        }
        //特色标签
        paddingSpecialTag(oceanMap, param.getShelfItemVO(), param.getProductM());
        //是否spu商品
        paddingSpuType(context, oceanMap, param.getProductM(), config);
        return JsonCodec.encode(oceanMap);
    }

    /**
     * 特色标签
     *
     * @param source
     * @param shelfItemVO
     * @param productM
     */
    private void paddingSpecialTag(Map<String, Object> source, ShelfItemVO shelfItemVO, ProductM productM) {
        if (shelfItemVO.getSpecialTags() == null || CollectionUtils.isEmpty(shelfItemVO.getSpecialTags().getTags())) {
            source.put("UGC_tag", -999);
            source.put("trade_rate_tag", -999);
            return;
        }
        RichLabelModel specialTag = shelfItemVO.getSpecialTags().getTags().get(0).getText();
        if (specialTag == null || StringUtils.isBlank(specialTag.getText())) {
            return;
        }
        //ugc标签
        List<String> ugcTags = PriceAboveTagsUtils.getRecommendTag(productM);
        String ugcTag = CollectionUtils.isNotEmpty(ugcTags) ? ugcTags.get(0) : null;
        source.put("UGC_tag", specialTag.getText().equals(ugcTag) ? 0 : -999);
        //竞争圈标签
        String tradeRateTag = PriceAboveTagsUtils.getTradeRateTag(productM);
        source.put("trade_rate_tag", specialTag.getText().equals(tradeRateTag) ? 0 : -999);
    }

    private void paddingSpuType(ActivityCxt context, Map<String, Object> source, ProductM productM, Config config) {
        if(StringUtils.isEmpty(productM.getSptSpuId()) || CollectionUtils.isEmpty(config.getSpuExps())){
            return;
        }
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        source.put("spu_type", DouHuUtils.hitAnySk(douHuMList, config.getSpuExps()) ? 1 : 0);
    }

    /**
     * 折扣和比价标签
     *
     * @param source
     * @param productM
     */
    public void paddingDiscountAndComparePriceLab(Map<String, Object> source, ProductM productM) {
        String discountTag = productM.getAttr(PriceDisplayUtils.ITEM_DISCOUNT_TAG);
        if (org.apache.commons.lang.StringUtils.isNotBlank(discountTag)) {
            source.put("count", discountTag);
        }
        String comparePriceTag = productM.getAttr(PriceDisplayUtils.ITEM_PRICE_POWER_TAG);
        if (org.apache.commons.lang.StringUtils.isNotBlank(comparePriceTag)) {
            source.put("label_text", comparePriceTag);
        }
    }

    private void paddingTopDisplayInfo(Map<String, Object> oceanMap, ProductM productM) {
        //是否堆头
        boolean isTopDisplay = ProductMAttrUtils.isTopDisplayProduct(productM);
        //0-普通团购，1-堆头
        oceanMap.put("module_show_type", isTopDisplay ? "1" : "0");
        oceanMap.put("type", ProductTypeEnum.getByType(productM.getProductType()).getDesc());
        oceanMap.put("module_name", "团购货架");
    }

    private void paddingCardLab(Map<String, Object> source, CardM cardM) {
        if (cardM == null) {
            return;
        }
        source.put("membercard_type", ShopUserProfileUtils.buildProfile(cardM.getShopCardList()));
        source.put("u_profile", ShopUserProfileUtils.buildProfile(cardM.getUserCardList()));
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 是否启用「堆头商品」打点
         */
        private boolean enableTopDisplayInfoLabs = false;

        /**
         * 是否启用会员卡信息打点
         */
        private boolean enableCardLabs = false;

        /**
         * spu货架实验
         */
        private List<String> spuExps;
    }
}
