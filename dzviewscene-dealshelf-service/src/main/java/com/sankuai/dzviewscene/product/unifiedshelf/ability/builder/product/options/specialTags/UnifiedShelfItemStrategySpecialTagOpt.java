package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags;

import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSpecialTagVP;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

@VPointOption(name = "策略特色标签", description = "策略特色标签", code = "UnifiedShelfItemStrategySpecialTagOpt")
public class UnifiedShelfItemStrategySpecialTagOpt extends UnifiedShelfItemSpecialTagVP<UnifiedShelfItemStrategySpecialTagOpt.Config> {

    @Autowired
    private SpecialTagStrategyFactory specialTagStrategyFactory;

    @Override
    public ItemSpecialTagVO compute(ActivityCxt context, Param param, Config config) {
        List<ShelfTagVO> shelfTagVOS = getTagsByStrategy(context, param, config);
        if (CollectionUtils.isEmpty(shelfTagVOS)) {
            return null;
        }
        ItemSpecialTagVO itemSpecialTagVO = new ItemSpecialTagVO();
        itemSpecialTagVO.setTags(shelfTagVOS);
        return itemSpecialTagVO;
    }

    private List<ShelfTagVO> getTagsByStrategy(ActivityCxt context, Param param, Config config) {
        SpecialTagBuildReq req = buildRequest(param.getProductM());
        // F型货架优先级策略
        if (ParamsUtil.getBooleanSafely(context.getParameters(), ShelfActivityConstants.Params.isFShelf) &&
                CollectionUtils.isNotEmpty(config.getStrategyByPriority())) {
            List<ShelfTagVO> resultTags = Lists.newArrayList();
            for (String strategy : config.getStrategyByPriority()) {
                SpecialTagStrategy specialTagStrategy = specialTagStrategyFactory.getSpecialTagStrategy(strategy);
                if (Objects.isNull(specialTagStrategy)) {
                    continue;
                }
                List<ShelfTagVO> tags = specialTagStrategy.build(req);
                if (CollectionUtils.isNotEmpty(tags)) {
                    resultTags.addAll(tags);
                }
            }
            return resultTags;
        }
        // 类目树策略
        String strategy = ShopCategoryConfigUtils.getHitConfig(context, config.getBackCategory2Strategy(), String.class);
        if (StringUtils.isNotEmpty(strategy)) {
            SpecialTagStrategy specialTagStrategy = specialTagStrategyFactory.getSpecialTagStrategy(strategy);
            if (Objects.isNull(specialTagStrategy)) {
                return null;
            }
            return specialTagStrategy.build(req);
        }
        return null;
    }

    private SpecialTagBuildReq buildRequest(ProductM productM) {
        SpecialTagBuildReq req = new SpecialTagBuildReq();
        req.setProductM(productM);
        return req;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 根据后台类目树配置 标签build策略
         * 配置格式{@link String}
         * example: {
         *     "backCategory2Strategy": {
         *         "configList": [{
         *             "backCategory": [4,2326],
         *             "config": "RepairStrategy"
         *         }]
         *     }
         * }
         */
        private ShopCategoryConfig backCategory2Strategy;

        /**
         * 策略优先级
         */
        private List<String> strategyByPriority;

    }
}
