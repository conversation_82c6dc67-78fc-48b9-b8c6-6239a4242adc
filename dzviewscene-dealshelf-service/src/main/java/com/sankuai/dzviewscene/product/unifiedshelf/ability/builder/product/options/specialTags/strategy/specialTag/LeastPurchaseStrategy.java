package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class LeastPurchaseStrategy implements SpecialTagStrategy {
    @Override
    public String getName() {
        return LeastPurchaseStrategy.class.getSimpleName();
    }

    @Override
    public String getStrategyDesc() {
        return "最近购买信息策略";
    }

    @Override
    public List<ShelfTagVO> build(SpecialTagBuildReq req) {
        if (Objects.isNull(req.getProductM())) {
            return null;
        }
        ProductM productM = req.getProductM();
        //预售库存紧张信息优于X小时购买信息展示
        if (PreSaleUtils.isPreSaleDealAndStockLess(productM)) {
            String stockStr = PreSaleUtils.getPreSaleStockPurchaseStr(productM);
            return Lists.newArrayList(SpecialTagsUtils.buildCommonTagVO(stockStr));
        }
        if (StringUtils.isNotBlank(productM.getPurchase())) {
            return Lists.newArrayList(SpecialTagsUtils.buildCommonTagVO(productM.getPurchase()));
        }
        return null;
    }
}
