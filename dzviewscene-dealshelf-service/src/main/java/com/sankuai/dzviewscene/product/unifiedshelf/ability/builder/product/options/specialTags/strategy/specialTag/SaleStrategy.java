package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class SaleStrategy implements SpecialTagStrategy {
    @Override
    public String getName() {
        return SaleStrategy.class.getSimpleName();
    }

    @Override
    public String getStrategyDesc() {
        return "销量特色标签";
    }

    @Override
    public List<ShelfTagVO> build(SpecialTagBuildReq req) {
        if (Objects.isNull(req.getProductM()) || Objects.isNull(req.getProductM().getSale()) || StringUtils.isBlank(req.getProductM().getSale().getSaleTag())) {
            return null;
        }
        return Lists.newArrayList(SpecialTagsUtils.buildCommonTagVO(req.getProductM().getSale().getSaleTag()));
    }
}
