package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.dianping.product.shelf.common.enums.ProductBaseInfoExtraKeyEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.DealSecKillUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.NationSubsidyPromoTagStrategy;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.utils.OceanConstantUtils;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@VPoint(name = "商品-Ocean 打点", description = "商品-labs", code = UnifiedShelfItemOceanLabsVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemOceanLabsVP<T> extends PmfVPoint<String, UnifiedShelfItemOceanLabsVP.Param, T> {

    public static final String CODE = "UnifiedShelfItemOceanLabsVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ShelfItemVO shelfItemVO;

        private ProductHierarchyNodeM nodeM;

        private long dpPoiId;

        private long mtPoiId;

        private int platform;

        private ShopM shop;

        private ProductM productM;

        private CardM cardM;

        private long filterId;
        
        private Integer cityId;
    }

    /**
     * 获取通用 Item 打点
     *
     * @param context
     * @param param
     * @return
     */
    protected Map<String, Object> getCommonOcean(ActivityCxt context, Param param) {
        Map<String, Object> oceanMap = new HashMap<>();
        paddingDefaultLab(oceanMap);
        oceanMap.put("cat_id", getCategoryId(param));
        oceanMap.put("deal_id", param.getShelfItemVO().getItemId());
        oceanMap.put("product_id", param.getShelfItemVO().getItemId());
        oceanMap.put("poi_id", getPoiId(param));
        oceanMap.put("title", getTitle(param.getShelfItemVO()));
        oceanMap.put("city_id", param.getCityId());
        paddingDiscountAndComparePriceLab(oceanMap, param.getProductM());
        paddingPromoDetailLab(context, oceanMap, param.getProductM());
        paddingRankSourceLab(oceanMap, param.getProductM());
        return oceanMap;
    }

    /**
     * 获取 SKU 打点
     *
     * @param context
     * @param param
     * @return
     */
    protected Map<String, Object> getSkuOcean(ActivityCxt context, Param param) {
        Map<String, Object> oceanMap = Maps.newHashMap();
        oceanMap.put("item_id", param.getNodeM().getProductId());
        oceanMap.put(OceanConstantUtils.TITLE, getTitle(param.getShelfItemVO()));
        oceanMap.put(OceanConstantUtils.PRICE, getPrice(param.getShelfItemVO()));
        oceanMap.put(OceanConstantUtils.INDEX, calSkuIndex(param.getNodeM()));
        paddingPromoDetailLab(context, oceanMap, param.getProductM());
        return oceanMap;
    }

    /**
     * 获取 SPU 打点
     * @param param
     * @return
     */
    protected Map<String, Object> getSpuOcean(Param param) {
        Map<String, Object> oceanMap = Maps.newHashMap();
        oceanMap.put("index", calSpuIndex(param.getNodeM()));
        //1单SPU 2多SPU
        oceanMap.put("type", param.getNodeM().getChildren().size() > 1 ? 2 : 1);
        oceanMap.put(OceanConstantUtils.TITLE, getTitle(param.getShelfItemVO()));
        return oceanMap;
    }

    /**
     * 计算SPU index
     * @param nodeM
     * @return
     */
    private int calSpuIndex(ProductHierarchyNodeM nodeM) {
        ProductHierarchyNodeM parent = nodeM.getParent();
        if(parent == null || CollectionUtils.isEmpty(parent.getChildren())) {
            return -999;
        }
        int index = 0;
        for (ProductHierarchyNodeM productHierarchyNodeM : parent.getChildren()) {
            if(productHierarchyNodeM.getProductType() != ProductTypeEnum.SPT_SPU.getType()) {
                continue;
            }
            if (productHierarchyNodeM.getIdentityKey().equals(nodeM.getIdentityKey())) {
                return index;
            }
            index ++;
        }
        return index;
    }

    /**
     * 计算SKU index
     * @param nodeM
     * @return
     */
    private int calSkuIndex(ProductHierarchyNodeM nodeM) {
        ProductHierarchyNodeM parent = nodeM.getParent();
        if (parent == null || CollectionUtils.isEmpty(parent.getChildren())) {
            return -999;
        }
        int index = 0;
        for (ProductHierarchyNodeM productHierarchyNodeM : parent.getChildren()) {
            if (productHierarchyNodeM.getIdentityKey().equals(nodeM.getIdentityKey())) {
                return index;
            }
            index ++;
        }
        return index;
    }

    private String getTitle(ShelfItemVO shelfItemVO) {
        if(shelfItemVO.getTitle() == null) {
            return "-999";
        }
        return shelfItemVO.getTitle().stream()
                .map(StyleTextModel::getText)
                .collect(Collectors.joining());
    }

    private void paddingDefaultLab(Map<String, Object> source) {
        source.put("activity_status", -999);
        source.put("alliance", -999);
        source.put("click_type", -999);
        source.put("customize_array", -999);
        source.put("deal_type", -999);
        source.put("label_content", -999);
        source.put("label_name", -999);
        source.put("select_name", -999);
        source.put("membercard_type", -999);
        source.put("u_profile", -999);
        source.put("view_type", -999);
        source.put("promotion_array", -999);
        source.put("promotion_title", -999);
    }

    /**
     * 折扣和比价标签
     * @param source
     * @param productM
     */
    public void paddingDiscountAndComparePriceLab(Map<String, Object> source, ProductM productM) {
        //折扣标签
        String discountTag = productM.getAttr(PriceDisplayUtils.ITEM_DISCOUNT_TAG);
        source.put("count", StringUtils.isNotBlank(discountTag) ? discountTag : "-999");
        //比价标签
        String comparePriceTag = productM.getAttr(PriceDisplayUtils.ITEM_PRICE_POWER_TAG);
        source.put("label_text", StringUtils.isNotBlank(comparePriceTag) ? comparePriceTag : "-999");
    }

    /**
     * 填充优惠明细打点
     *
     * @param context
     * @param productM
     */
    public void paddingPromoDetailLab(ActivityCxt context, Map<String, Object> source, ProductM productM) {
        if (productM == null || productM.getBestPromoPrice() == null
                || CollectionUtils.isEmpty(productM.getBestPromoPrice().getPromoItemList())) {
            return;
        }
        source.put("promotion_array", productM.getBestPromoPrice().getPromoItemList());
        if (PriceUtils.isSupportDp4NationalSubsidy(ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform)) && PriceUtils.hasNationalSubsidy(productM)) {
            source.put("promotion_title", NationSubsidyPromoTagStrategy.buildNationalSubsidyText(productM));
            return;
        }
        source.put("promotion_title", getPromoTagDesc(productM));
    }

    /**
     * 排序来源打点
     * @param source
     * @param productM
     */
    public void paddingRankSourceLab(Map<String, Object> source, ProductM productM) {
        ProductBaseInfoExtraKeyEnum rankSource = Stream.of(ProductBaseInfoExtraKeyEnum.searchProduct, ProductBaseInfoExtraKeyEnum.shopRecommend,
                        ProductBaseInfoExtraKeyEnum.algorithmRecommend, ProductBaseInfoExtraKeyEnum.bottomProduct)
                .filter(key -> "true".equals(productM.getAttr(key.getVal())))
                .findFirst()
                .orElse(null);
        source.put("dz_shelf_sequence_name", rankSource != null ? rankSource.getVal() : "-999");
    }

    private String getPromoTagDesc(ProductM productM) {
        if(DealSecKillUtils.isSecKillDeal(productM)) {
            return "限时秒杀" + productM.getBestPromoPrice().getPromoTag();
        }
        int promoTagType = productM.getBestPromoPrice().getPromoTagType();
        if (promoTagType == PromoTagTypeEnum.Default.getCode()) {
            return "-999";
        } else if (promoTagType == PromoTagTypeEnum.Other.getCode()) {
            return "特惠促销" + productM.getBestPromoPrice().getPromoTag();
        }
        for (PromoTagTypeEnum tagTypeEnum : PromoTagTypeEnum.values()) {
            if (tagTypeEnum.getCode() == promoTagType) {
                return tagTypeEnum.getDesc() + productM.getBestPromoPrice().getPromoTag();
            }
        }
        return "-999";
    }

    private int getCategoryId(Param param) {
        if (param.getShop() == null) {
            return 0;
        }
        return param.getShop().getCategory();
    }

    private long getPoiId(Param param) {
        if (VCPlatformEnum.MT.getType() == param.getPlatform()) {
            return param.getMtPoiId();
        }
        return param.getDpPoiId();

    }

    private static String getPrice(ShelfItemVO itemVO) {
        String price = itemVO.getSalePrice();
        if (StringUtils.isEmpty(price)) {
            return StringUtils.EMPTY;
        }
        return itemVO.getSalePrice();
    }
}
