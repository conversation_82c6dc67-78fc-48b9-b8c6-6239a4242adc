/*
 * Create Author  : liyanmin
 * Create Date    : 2024-07-01
 * Project        :
 * File Name      : PriceUtils.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.utils;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.common.helper.LionKeys;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-07-01
 * @since dzviewscene-dealshelf-home 1.0
 */
@Slf4j
public class PriceUtils {

    /**
     * 兼容下比价，取最优promoPrice，与到手价取值逻辑保持一致
     * @param productM
     * @param cardM
     * @return
     */
    public static ProductPromoPriceM getUserHasPromoPrice(ProductM productM, CardM cardM){
        ProductPromoPriceM directPromo = null;
        try {
            if(Objects.isNull(productM) || CollectionUtils.isEmpty(productM.getPromoPrices())){
                return null;
            }
            directPromo = productM.getPromoPrices().stream().filter(o -> o.getPromoType() == PromoTypeEnum.DIRECT_PROMO.getType()).findFirst().orElse(null);
            //含商家会员卡
            MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getHasMerchantMemberPromo(productM);
            //有商家会员卡优惠且没有倒挂，返回商家会员卡优惠,商家会员卡跟折扣卡玩乐卡互斥的
            if(Objects.nonNull(merchantMemberPromo) && Objects.nonNull(merchantMemberPromo.getProductPromoPrice()) && !CardPromoUtils.isCardDaoGuaPromo(directPromo, merchantMemberPromo.getProductPromoPrice())){
                return merchantMemberPromo.getProductPromoPrice();
            }
            //含折扣卡、玩乐卡等
            ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
            //有卡优惠且卡优惠没有倒挂，返回持卡优惠
            if (Objects.nonNull(cardPromo) && !CardPromoUtils.isCardDaoGuaPromo(directPromo, cardPromo)) {
                return cardPromo;
            }
            return directPromo;
        } catch (Exception e) {
            log.error(String.format("getUserHasPromoPrice.error,productM:%s", JsonCodec.encode(productM)),e);
            return directPromo;
        }
    }

    /**
     * 判断商品是否有国补
     * @param productM
     * @return
     */
    public static boolean hasNationalSubsidy(ProductM productM){
        // 国补开关，如果关闭开关则不展示国补相关内容
        if(!Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)
                || Objects.isNull(productM) || CollectionUtils.isEmpty(productM.getPromoPrices())){
            return false;
        }
        return productM.getPromoPrices().stream().map(ProductPromoPriceM::getNationalSubsidyPrice).anyMatch(price -> {
            return Objects.nonNull(price) && !price.equals(BigDecimal.ZERO);
        });
    }

    /**
     * 国补标签是否支持点评，默认不支持
     * @param platform
     * @return
     */
    public static boolean isSupportDp4NationalSubsidy(int platform) {
        // 支持点评开关，优先级更高
        if (Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.nationalSubsidy.isSupportDp4NationalSubsidy", false)) {
            return true;
        }
        // 如果是点评则不支持透出国补
        return PlatformUtil.isMT(platform);
    }

    public static String getNationSubsidyPriceTag(ProductM productM){
        return productM.getPromoPrices().stream().map(ProductPromoPriceM::getNationalSubsidyPrice)
                .filter(price -> {
                    return Objects.nonNull(price) && price.compareTo(BigDecimal.ZERO) > 0;
                })
                .map(price -> {return price.stripTrailingZeros().toPlainString();})
                .filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY);
    }

    public static String getNationSubsidyMarketPriceTag(ProductM productM){
        return productM.getPromoPrices().stream().map(ProductPromoPriceM::getNationalSubsidyMarketPrice)
                .filter(price -> {
                    return Objects.nonNull(price) && price.compareTo(BigDecimal.ZERO) > 0;
                })
                .map(price -> {return price.stripTrailingZeros().toPlainString();})
                .filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY);
    }
}
