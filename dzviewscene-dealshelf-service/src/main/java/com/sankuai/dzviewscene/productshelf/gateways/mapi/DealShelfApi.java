package com.sankuai.dzviewscene.productshelf.gateways.mapi;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceEngine;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceExecutionEngine;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.product.shelf.utils.GatewayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.faulttolerance.ActivityContextRequestBuilder;
import com.sankuai.dzviewscene.shelf.faulttolerance.dealshelf.DealShelfFTConfiguration;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 请求货架的首屏数据，也适用于一次返回整个货架数据的情况
 *
 * @auther: liweilong06
 * @date: 2020/7/6 7:26 上午
 */
@URL(url = "/api/dzviewscene/productshelf/dzdealshelf")
@Service
public class DealShelfApi implements API, AppContextAware, UserIdAware, MTUserIdAware {

    /**
     * 统一商户ID
     */
    @Setter
    private String shopuuid;

    /**
     * 定位的关键字
     */
    @Setter
    private String searchkeyword;

    /**
     * 城市Id
     */
    @Setter
    private Integer cityid;

    /**
     * 用户定位城市id
     */
    @Setter
    private Integer locationcityid;

    /**
     * 经度
     */
    @Setter
    private Double lng;

    /**
     * 纬度
     */
    @Setter
    private Double lat;

    /**
     * 经纬度坐标类型，货架默认为GCJ02
     * com.sankuai.nib.mkt.common.base.enums.RealGpsCoordTypeEnum
     */
    @Setter
    private String coordType = "GCJ02";

    /**
     * 商户Id
     */
    @Setter
    private long shopid; // poiMigrate

    /**
     * 来自后端分配@float.lu
     */
    @Setter
    private String sceneCode;

    /**
     * 平台 {@link VCClientTypeEnum}
     * 100： dpapp
     * 101： m
     * 200： mtapp
     * 201： i
     */
    @Setter
    private int platform;

    /**
     * 客户端类型：ios | android | harmony | 空字符串
     */
    @Setter
    private String client = "";

    /**
     * 版本号
     */
    @Setter
    private String version;

    /**
     * 设备ID，dpId or uuid
     */
    @Setter
    private String deviceId;

    /**
     * unionid
     * 美团小程序的openId
     */
    @Setter
    private String unionId;

    /**
     * 点评用户ID
     */
    @Setter
    private long dpUserId;

    /**
     * 美团用户ID
     */
    @Setter
    private long mtUserId;

    /**
     * 下挂商品Id 【旧】
     * 示例："1,2,3"
     * 解析工具如下：
     * {@link ParamUtil#getSummaryDealIds(java.lang.String, java.lang.String)}
     */
    @Setter
    private String productids;

    /**
     * 下挂商品Id 【新】
     * Ex：{"deal":"1,2,3","spu":"4,5,6"}
     * deal - 团单， spu - 泛商品
     * 解析工具如下：
     * {@link ParamUtil#getSummaryDealIds(java.lang.String, java.lang.String)}
     */
    @Setter
    private String summarypids;

    /**
     * 用户选中/飘红的商品，需要强制的置顶
     */
    @Setter
    private String anchorgoodid;

    /**
     * 上游商品类型
     */
    @Setter
    private String biztype;

    /**
     * 货架模块版本，由前端维护·
     */
    @Setter
    private int shelfversion;

    /**
     * MTSI反爬标识，其值来源于请求头，直接透传
     */
    @Setter
    private String mtsiflag;

    /**
     * 页面来源标记，用于在跳转链接上加来源标识后缀,前端直接传"mtlm=xxx"这种key+value样式的信息 需让前端先encode，后端会自动解析
     */
    @Setter
    private String pagesource;

    /**
     * 价格一致率透传加密字符串
     */
    @Setter
    private String pricecipher;

    /**
     * 猜喜侧传入商品，可能是到综商品、到餐商品
     */
    @Setter
    private String recommendinfo;

    /**
     * 神会员点位，废弃，后端自动识别
     * com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil#getPosition(int, java.lang.String)
     */
    @Deprecated
    @Setter
    private String position;

    @Setter
    private Integer pageindex;

    @Setter
    private Integer pagesize;

    @Setter
    private String pagination;

    /**
     * regionId，到家set化用
     */
    @Setter
    private String wtt_region_id;

    /**
     * 用于货架首屏tab锚点
     */
    @Setter
    private String anchorfilterid;

    /**
     * 自定义业务字段前后端交互信息，Map<String,String>格式的JSON字符串
     * key参考
     * @see com.sankuai.dzviewscene.productshelf.vu.enums.DealShelfCustomInfoKeyEnum
     * value为前后端约定的具体值
     */
    @Setter
    private String custominfo;

    /**
     * 屏幕高度，用户提前获取团详页布局信息，做闪开方案
     */
    @Setter
    private Double deviceHeight;

    @Setter
    private String appid;

    @Setter
    private String extra;

    @Setter
    private String channelType;

    private FaultToleranceEngine faultToleranceEngine = new FaultToleranceExecutionEngine();

    @Resource
    private DealShelfFTConfiguration dealShelfFaultToleranceConfiguration;

    @Resource
    private ActivityContextRequestBuilder activityContextRequestBuilder;

    @Override
    public Object execute() {
        GatewayUtils.ignoreNullField(platform);
        ActivityContextRequest request = activityContextRequestBuilder.buildActivityContextRequest(getActivityContextRequest());
        LogUtils.recordKeyMsg(getUserId(),"dzdealshelfEntrance",request);
        Object result = faultToleranceEngine.execute(request, dealShelfFaultToleranceConfiguration);
        LogUtils.recordKeyMsg(getUserId(),"dzdealshelfEnd",result);
        if (StringUtils.isNotBlank(request.getShopIdInconsistencyFlag()) && LionConfigHelper.shopIdInconsistencySwitch()) {
            LogUtils.recordKeyMsg("dzdealshelfEntranceshopIdInconsistency", request);
        }
        if (request.getShopId() > Integer.MAX_VALUE && LionConfigHelper.longShopIdLogSwitch(request.getShopId())){
            LogUtils.recordKeyMsg("longshopiddzdealshelfEntrance", request);
        }
        return result;
    }

    private ActivityContextRequest getActivityContextRequest() {
        ActivityContextRequest request = new ActivityContextRequest();
        request.setSceneCode(sceneCode);
        request.setSearchKeyword(searchkeyword);
        request.setShopId(shopid);
        request.setShopUuid(shopuuid);
        request.setUnionId(unionId);
        request.setVersion(version);
        request.setChannel(ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        request.setUserId(getUserId());
        request.setPlatform(platform);
        request.setLng(lng);
        request.setCityId(cityid);
        request.setLocationCityId(locationcityid);
        request.setClient(client);
        request.setDeviceId(deviceId);
        request.setLat(lat);
        request.setCoordType(coordType);
        request.setTopProductIds(productids);
        request.setSummaryProductIds(summarypids);
        request.setAnchorGoodId(anchorgoodid);
        request.setBizType(biztype);
        request.setShelfVersion(shelfversion);
        request.setMtSIFlag(mtsiflag);
        request.setPageSource(pagesource);
        request.setRecommendinfo(recommendinfo);
        request.setPricecipher(pricecipher);
        request.setPosition(position);
        request.setWttRegionId(wtt_region_id);
        request.setPageindex(Optional.ofNullable(pageindex).orElse(0));
        request.setPagesize(Optional.ofNullable(pagesize).orElse(0));
        request.setPagination(pagination);
        request.setFilterBtnId(NumberUtils.toLong(anchorfilterid));
        request.setCustomInfo(custominfo);
        if (deviceHeight != null) {
            request.setDeviceHeight((int) Math.round(deviceHeight));
        }
        request.setAppId(appid);
        request.setOpenId(VCClientTypeEnum.MT_XCX.getCode() == platform ? unionId : null);
        request.setExtra(extra);
        request.setChannelType(channelType);
        return request;
    }

    @Override
    public void setAppContext(AppContext appContext) {
        deviceId = getDeviceId(appContext.getDpid());
        unionId = getUnionId(appContext.getUnionid());
        client = getClient(appContext.getClient());
        version = getVersion(appContext.getVersion());
        platform = getPlatform(appContext.getPlatform());
        mtsiflag = getMtSIFlag(appContext.getMtsiflag());
    }

    private String getMtSIFlag(String mtSIFlag) {
        if (StringUtils.isNotEmpty(mtsiflag)) {
            return mtsiflag;
        }
        return mtSIFlag;
    }

    private int getPlatform(String currentPlatform) {
        if (platform > 0) return platform;
        if (StringUtils.isEmpty(currentPlatform) || currentPlatform.equals("dp")) {
            return VCClientTypeEnum.DP_APP.getCode();
        }
        return VCClientTypeEnum.MT_APP.getCode();
    }

    private String getDeviceId(String currentDeviceId) {
        if (StringUtils.isEmpty(deviceId)) return currentDeviceId;
        return deviceId;
    }

    private String getUnionId(String currentUnionId) {
        if (StringUtils.isEmpty(unionId)) return currentUnionId;
        return unionId;
    }

    private String getClient(String currentClient) {
        if (StringUtils.isEmpty(client)) return currentClient;
        return client;
    }

    private String getVersion(String currentVersion) {
        if (StringUtils.isEmpty(version)) return currentVersion;
        return version;
    }


    @Override
    public void setMTUserId(long userId) {
        if (mtUserId > 0) {
            return;
        }
        this.mtUserId = userId;
    }

    @Override
    public void setUserId(long userId) {
        if (dpUserId > 0) {
            return;
        }
        this.dpUserId = userId;
    }

    private long getUserId() {
        if (PlatformUtil.isMT(platform)) {
            return mtUserId;
        }
        return dpUserId;
    }
}
