package com.sankuai.dzviewscene.shelf.faulttolerance;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheGrayUtils;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheMethodEnum;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopCacheM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 活动上下文请求参数构造器
 * Created by zhangsuping on 2021/9/24.
 */
@Component
public class ActivityContextRequestBuilder {

    // 商户后台类目
    private static final String BACK_CAT_FIELD = "backMainCategoryPath";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private CacheCompositeAtomService cacheCompositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.shop.rpc.timeout.config", defaultValue = "150")
    private long timeout;

    /**
     * 构造上线文请求
     *
     * @param request
     * @return
     */
    public ActivityContextRequest buildActivityContextRequest(ActivityContextRequest request) {
        // 点评平台
        if (!PlatformUtil.isMT(request.getPlatform())) {
            request.setDpShopId(request.getShopId());
            request.setDpCityId(request.getCityId());
            addShopDetailIfNeed(request.getShopId(), request);
            return request;
        }
        //添加美团商户详情信息
        addMtShopDetailIfNeed(request);
        return request;
    }

    private void addMtShopDetailIfNeed(ActivityContextRequest request) {
        // 美团平台
        try {
            CompletableFuture<Void> dpShopIdFuture = getDpPoiIdByMt(request).thenAccept((dpShopId) -> {
                request.setDpShopId(dpShopId != null ? dpShopId : 0);
                addShopDetailIfNeed(request.getDpShopId(), request);
            });
            CompletableFuture<Void> dpCityIdFuture = getDpCityIdByMt(request).thenAccept((dpCityId) -> {
                request.setDpCityId(dpCityId != null ? dpCityId : 0);
            });
            CompletableFuture.allOf(dpShopIdFuture, dpCityIdFuture).join();
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    private void addShopDetailIfNeed(long dpShopId, ActivityContextRequest request) {
        ShopM shopM = loadShopModel(dpShopId);
        if (shopM == null) {
            return;
        }
        // 加一下shopId long 不一致标识
        addDiffShopTag(shopM, request);
        request.setShopType(shopM.getShopType());
        request.setShopMainCategoryId(shopM.getCategory());
        request.setUseType(shopM.getUseType());
        //点评侧且shopuuid为空时重置shopuuid
        if (!PlatformUtil.isMT(request.getPlatform()) && StringUtils.isEmpty(request.getShopUuid())) {
            request.setShopUuid(shopM.getShopUuid());
        }
        request.setShopM(shopM);
    }

    private void addDiffShopTag(ShopM shopM, ActivityContextRequest request) {
        if (shopM.getLongShopId() != shopM.getShopGroupId() && LionConfigHelper.shopIdInconsistencySwitch()
                && shopM.getLongShopId() < Integer.MAX_VALUE && shopM.getShopGroupId() > Integer.MAX_VALUE) {
            request.setShopIdInconsistencyFlag("shopId=" + shopM.getLongShopId() + "&shopGroupId=" + shopM.getShopGroupId());
            Cat.logEvent("shopIdInconsistencyFlag", request.getShopIdInconsistencyFlag());
        }
    }

    private ShopM loadShopModel(long dpShopId) {
        if(dpShopId == 0){
            return null;
        }
        try {
            DpPoiRequest dpPoiRequest = buildDpPoiRequest(Lists.newArrayList(dpShopId));
            boolean cacheSwitch = CacheGrayUtils.graySwitch(dpShopId, VCPlatformEnum.DP.getType(), CacheMethodEnum.SHOP_INFO_DP.getCode());
            if (cacheSwitch) {
                return cacheCompositeAtomService.findShopsByDpShopId(dpPoiRequest).thenApply(this::buildShopM).get(timeout, TimeUnit.MILLISECONDS);
            }
            return compositeAtomService.findShopsByDpShopIds(dpPoiRequest).thenApply(dpPoiDTOS -> {
                if (CollectionUtils.isEmpty(dpPoiDTOS) || Objects.isNull(dpPoiDTOS.get(0))) {
                    return null;
                }
                return buildShopM(dpPoiDTOS.get(0));
            }).get(timeout, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    private DpPoiRequest buildDpPoiRequest(ArrayList<Long> dpPoiIds) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        dpPoiRequest.getFields().add(BACK_CAT_FIELD);
        return dpPoiRequest;
    }

    private ShopM buildShopM(DpPoiDTO dpPoiDTO) {
        ShopM shopM = new ShopM();
        shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
        shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
        shopM.setShopGroupId(dpPoiDTO.getShopGroupId() == null ? 0 : dpPoiDTO.getShopGroupId());
        shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
        shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
        shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
        shopM.setBackCategory(ModelUtils.extractBackCat(dpPoiDTO));
        shopM.setUseType(dpPoiDTO.getUseType() == null ? 0 : dpPoiDTO.getUseType());
        shopM.setShopName(dpPoiDTO.getShopName());
        shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
        shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
        shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
        return shopM;
    }

    private ShopM buildShopM(ShopCacheM shopCacheM) {
        if (shopCacheM == null) {
            return null;
        }
        ShopM shopM = new ShopM();
        shopM.setShopId((int) shopCacheM.getShopId());
        shopM.setLongShopId(shopCacheM.getShopId());
        shopM.setShopUuid(shopCacheM.getShopUuid());
        shopM.setShopType(shopCacheM.getShopType());
        shopM.setCategory(shopCacheM.getCategory());
        shopM.setBackCategory(shopCacheM.getBackCategory());
        shopM.setUseType(shopCacheM.getUseType());
        shopM.setShopName(shopCacheM.getShopName());
        shopM.setLat(shopCacheM.getLat());
        shopM.setLng(shopCacheM.getLng());
        shopM.setCityId(shopCacheM.getCityId());
        return shopM;
    }

    private CompletableFuture<Long> getDpPoiIdByMt(ActivityContextRequest request) {
        boolean cacheSwitch = CacheGrayUtils.graySwitch(request.getShopId(), request.getPlatform(), CacheMethodEnum.SHOP_ID_MT_2_DP.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.getDpByMtPoiIdL(request.getShopId());
        } else {
            return compositeAtomService.getDpByMtPoiIdL(request.getShopId());
        }
    }

    private CompletableFuture<Integer> getDpCityIdByMt(ActivityContextRequest request) {
        boolean cacheSwitch = CacheGrayUtils.graySwitch(request.getShopId(), request.getPlatform(), CacheMethodEnum.CITY_ID_MT_2_DP.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.getDpCityIdByMt(request.getCityId());
        } else {
            return compositeAtomService.getDpCityIdByMt(request.getCityId());
        }
    }
}
