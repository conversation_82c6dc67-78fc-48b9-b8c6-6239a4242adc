package com.sankuai.dzviewscene.shelf.faulttolerance.req;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import lombok.Data;
import lombok.Setter;

import java.util.List;

/**
 * 活动请求
 * Created by zhangsuping on 2021/9/23.
 */
@Data
public class ActivityContextRequest {

    /**
     * 筛选项ID
     */
    private long filterBtnId;

    /**
     * 多条件筛选参数，逗号分隔
     */
    private String filterParams;

    /**
     * 统一商户ID
     */
    private String shopUuid;

    /**
     * 定位的关键字
     */
    private String searchKeyword;

    /**
     * 城市Id
     */
    private Integer cityId;

    /**
     * 点评城市id
     */
    private Integer dpCityId;

    /**
     * 用户定位城市id
     */
    private Integer locationCityId;

    /**
     * 模块名
     */
    private String moduleName;

    /**
     * 页起始号
     */
    private int pageindex =1;

    /**
     * 页大小
     */
    private int pagesize = 6;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经纬度坐标类型
     */
    private String coordType;

    /**
     * 商户Id
     */
    private long shopId;

    /**
     * 点评商户ID
     */
    private long dpShopId;

    /**
     * 点评商户类型
     */
    private int shopType;

    /**
     * 门店类型
     * {@link <a href="https://km.sankuai.com/collabpage/30032406">门店类型字段说明</a>}
     */
    private int useType;

    /**
     * 点评商户前台主分类ID
     */
    private int shopMainCategoryId;

    /**
     * 来自后端分配@float.lu
     */
    private String sceneCode;

    /**
     * MTSI反爬标识，其值来源于请求头，直接透传
     */
    private String mtSIFlag;

    /**
     * 平台 {@link VCClientTypeEnum}
     * 100： dpapp
     * 101： m
     * 200： mtapp
     * 201： i
     */
    private int platform;

    /**
     * 客户端类型：ios | android | 空字符串
     */
    private String client = "android";

    /**
     * 商品ID
     */
    private long productId;

    /**
     * 分享码， 用于分享场景
     */
    private String shareCode;

    /**
     * 商品版本号
     */
    private long productVersion;

    /**
     * SKU ID
     */
    private int productItemId;

    /**
     * 选择日期，用于需要选择某个日期的商品信息做展示
     */
    private Long purchaseDate;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 请求来源参数字段, 用于区分是筛选接口还是商品列表接口
     */
    String channel = "channel";

    /**
     * 版本号
     */
    private String version;

    /**
     * 设备ID，dpId or uuid
     */
    private String deviceId;

    /**
     * unionid
     */
    private String unionId;

    /**
     * 用户ID
     */
    private long userId;

    /**
     * 下挂商品Id 【新】
     * Ex：{"deal":"1,2,3","spu":"4,5,6"}
     * deal - 团单， spu - 泛商品
     * 解析工具如下：
     * {@link ParamUtil#getSummaryDealIds(java.lang.String,java.lang.String)}
     */
    private String summaryProductIds;

    /**
     * 置顶商品ID列表，格式：id列表，逗号分隔
     */
    private String topProductIds;

    /**
     * 资源位
     */
    private String spaceKey;

    /**
     * 货架模块版本，由前端维护·
     */
    private int shelfVersion;

    /**
     * 页面来源，用于跳转链接额外信息拼接，前端透传
     */
    private String pageSource;

    /**
     * 猜喜商品ID，可能是到综商品、到餐商品
     */
    private String recommendinfo;

    /**
     * 用户选中/飘红的商品，需要强制的置顶
     */
    private String anchorGoodId;

    /**
     * 上游商品类型
     */
    private String bizType;

    /**
     * 价格一致率透传加密字符串
     */
    private String pricecipher;

    /**
     * 点位
     */
    private String position;

    /**
     * 刷新标志
     */
    private String refreshTag;

    /**
     * 到家set化regionId
     */
    private String wttRegionId;

    /**
     * 货架是否分页,传1表示分页
     */
    private String pagination;

    /**
     * 自定义扩展字段信息
     */
    private String customInfo;

    /**
     * 加载的商户信息
     */
    private ShopM shopM;

    /**
     * 屏幕高度，用户提前获取团详页布局信息，做闪开方案
     */
    private Integer deviceHeight;

    /**
     * appId https://km.sankuai.com/collabpage/2104641030
     */
    private String appId;

    /**
     * 小程序的openId
     * 因为下游不确定复用deviceId会有什么问题，所以独立出来字段
     */
    private String openId;

    /**
     * 运营配置预览标识
     */
    private List<String> operatorPreviewConfigTags;

    /**
     * 模拟的斗斛实验结果
     */
    private List<String> mockDouHuResult;

    /**
     * 渠道来源（较pageSource具有更高层级聚合）
     * pageSource 记录具体页面级来源标识，多个不同落地页可同属一个渠道类型
     */
    private String channelType;

    /**
     * shopId查询不一致标识
     */
    private String shopIdInconsistencyFlag;

}
