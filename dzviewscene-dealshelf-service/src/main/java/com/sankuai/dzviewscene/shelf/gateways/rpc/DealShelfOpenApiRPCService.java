package com.sankuai.dzviewscene.shelf.gateways.rpc;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.dealshelf.dto.OpenApiResponse;
import com.sankuai.dzviewscene.dealshelf.dto.baidumap.BaiduMapShelfDTO;
import com.sankuai.dzviewscene.dealshelf.dto.baidumap.BaiduMapShelfDealDTO;
import com.sankuai.dzviewscene.dealshelf.dto.baidumap.DealPromoDetailDTO;
import com.sankuai.dzviewscene.dealshelf.dto.lixiang.LiXiangMapActivityTagDTO;
import com.sankuai.dzviewscene.dealshelf.dto.lixiang.LiXiangMapDealPromoDetailDTO;
import com.sankuai.dzviewscene.dealshelf.dto.lixiang.LiXiangMapShelfDTO;
import com.sankuai.dzviewscene.dealshelf.dto.lixiang.LiXiangMapShelfDealDTO;
import com.sankuai.dzviewscene.dealshelf.dto.tencentmap.ShelfComponentDTO;
import com.sankuai.dzviewscene.dealshelf.dto.tencentmap.ShelfDealDTO;
import com.sankuai.dzviewscene.dealshelf.service.DealShelfOpenApiService;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemextra.PaddingJsonItemExtraOpt;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 团购货架 OpenApi 接口
 * <AUTHOR>
 * @date 2022/5/23
 */
@Service(url = "com.sankuai.dzviewscene.dealshelf.service.DealShelfOpenApiService")
public class DealShelfOpenApiRPCService implements DealShelfOpenApiService {

    private static final int PLATFORM = VCClientTypeEnum.MT_XCX.getCode();

    private static final String SCENE_CODE = "tencent_map_deal_shelf";

    private static final String SCENE_CODE_BAIDU = "baidu_map_deal_shelf";

    private static final String SCENE_CODE_LI_XIANG = "lixiang_map_deal_shelf";

    /**
     * 美团小程序跳转链接
     */
    private static final String JUMP_URL_FORMAT = "/gnc/pages/grouping/index?id=%d&utm_source=offsite_play_tencentmap_dealshelf";

    /**
     * 美团 App 版本号，上游拼团数据源会校验版本号大于 9.5.0 才有数据
     */
    private static final String MT_APP_VERSION = "11.19.400";

    private static final int BAI_DU_MAP_CLIENT_TYPE = 101100;
    public static final String JSON_START = "{";

    @Autowired
    private AtomFacadeService facadeService;
    @Resource
    private ActivityEngine activityEngine;
    @Autowired
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.shopId.list", defaultValue = "[]")
    private List<Long> shopIdList;

    @Override
    public OpenApiResponse<ShelfComponentDTO> getDealShelfForTencentMap(String openShopId, String openAppKey) {
        //参数校验
        OpenApiResponse<ShelfComponentDTO> openApiResponse = new OpenApiResponse<>();
        if (StringUtils.isEmpty(openShopId)) {
            openApiResponse.setParamError("openShopId empty");
            return openApiResponse;
        }
        if (StringUtils.isEmpty(openAppKey)) {
            openApiResponse.setParamError("openAppKey empty");
            return openApiResponse;
        }
        //获取poi
        CompletableFuture<Long> poiIdFuture = facadeService.getMtShopIdByOpenShopId(openAppKey, openShopId);
        Long mtPoiId = poiIdFuture.join();
        if (mtPoiId == null || mtPoiId < 1) {
            return getDefaultSuccessResponse(null);
        }
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(buildActivityRequest(mtPoiId)); // poiMigrate
        if (activityResponse == null || activityResponse.getResult() == null) {
            return getDefaultSuccessResponse(null);
        }
        DzShelfResponseVO shelfResponseVO = activityResponse.getResult().join();
        ShelfComponentDTO shelfComponentDTO = dzShelfResponseVO2ShelfComponentDTO(shelfResponseVO);
        return getDefaultSuccessResponse(shelfComponentDTO);
    }

    @Override
    public OpenApiResponse<BaiduMapShelfDTO> getDealShelfForBaiduMap(long dpPoiId) {
        //参数校验
        OpenApiResponse<BaiduMapShelfDTO> openApiResponse = new OpenApiResponse<>();
        if (dpPoiId < 1) {
            openApiResponse.setParamError("dpPoiId empty");
            return openApiResponse;
        }

        ActivityResponse<DzShelfResponseVO> activityResponse;
        //查询门店
        // poiMigrate
        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.DealShelfOpenApiRPCService.getScene())) {
            CompletableFuture<List<DpPoiDTO>> shopFuture = compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpPoiId)));
            List<DpPoiDTO> dpPoiDTOList;
            try {
                dpPoiDTOList =  shopFuture.get(100, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "findShopsByDpShopIds"), e);
                dpPoiDTOList = Collections.emptyList();
            }
            if (CollectionUtils.isEmpty(dpPoiDTOList) || Objects.isNull(dpPoiDTOList.get(0))) {
                openApiResponse.setParamError("dpPoiId error");
                return openApiResponse;
            }

            //构造请求并执行
            activityResponse = activityEngine.execute(buildActivityRequestOfBaiduMap(dpPoiDTOList.get(0)));
        } else {
            // 原逻辑
            ShopDTO shopDTO = loadShop(dpPoiId);
            if (shopDTO == null) {
                openApiResponse.setParamError("dpPoiId error");
                return openApiResponse;
            }
            //构造请求并执行
            activityResponse = activityEngine.execute(buildActivityRequestOfBaiduMap(shopDTO));
        }

        if (activityResponse == null || activityResponse.getResult() == null) {
            return getDefaultSuccessResponse(null);
        }
        List<DzItemVO> dzItemVOList = extractDealsFromResp(activityResponse.getResult().join());
        if (CollectionUtils.isEmpty(dzItemVOList)) {
            return getDefaultSuccessResponse(null);
        }
        //模型转换
        return getDefaultSuccessResponse(buildBaiduMapShelfDTO(dzItemVOList, dpPoiId));
    }

    private ActivityRequest buildActivityRequestOfBaiduMap(DpPoiDTO dpPoiDTO) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, SCENE_CODE_BAIDU);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, BAI_DU_MAP_CLIENT_TYPE);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "");
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
        activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, dpPoiDTO.getShopId().intValue());
        activityRequest.addParam(ShelfActivityConstants.Params.dpPoiIdL, dpPoiDTO.getShopId());
        //这里就塞 ctxShop 是防止引擎内部再次调用，放大流量
        activityRequest.addParam(ShelfActivityConstants.Ctx.ctxShop, buildShopMForDpPoiDTO(dpPoiDTO));
        activityRequest.addParam("spaceKey", DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY);
        return activityRequest;
    }

    private ShopM buildShopMForDpPoiDTO(DpPoiDTO dpPoiDTO) {
        ShopM shopM = new ShopM();
        if (Objects.isNull(dpPoiDTO)) {
            throw new IllegalArgumentException("dpPoiDTO is empty");
        }
        shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
        shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
        shopM.setShopName(dpPoiDTO.getShopName());
        shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
        shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
        shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
        shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
        shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
        shopM.setStatus(dpPoiDTO.getPower() == null ? -1 : dpPoiDTO.getPower());
        shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
        return shopM;
    }

    private DpPoiRequest buildDpPoiRequest(ArrayList<Long> dpPoiIds) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }

    @Override
    public OpenApiResponse<LiXiangMapShelfDTO> getDealShelfForLiXiangMap(long mtPoiId) {
        //参数校验
        OpenApiResponse<LiXiangMapShelfDTO> openApiResponse = new OpenApiResponse<>();
        if (mtPoiId < 1) {
            openApiResponse.setParamError("mtPoiId empty");
            return openApiResponse;
        }
        //构造请求并执行
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(buildActivityRequestOfLiXiangMap(mtPoiId));
        if (activityResponse == null || activityResponse.getResult() == null) {
            return getDefaultSuccessResponse(null);
        }
        List<DzItemVO> dzItemVOList = extractDealsFromResp(activityResponse.getResult().join());
        if (CollectionUtils.isEmpty(dzItemVOList)) {
            return getDefaultSuccessResponse(null);
        }
        //模型转换
        return getDefaultSuccessResponse(buildLiXiangMapShelfDTO(dzItemVOList));
    }

    private LiXiangMapShelfDTO buildLiXiangMapShelfDTO(List<DzItemVO> dzItemVOList) {
        LiXiangMapShelfDTO shelf = new LiXiangMapShelfDTO();
        List<LiXiangMapShelfDealDTO> dealList = new ArrayList<>(dzItemVOList.size());
        for (int index = 0; index < dzItemVOList.size(); index++) {
            dealList.add(buildLiXiangMapShelfDealDTO(dzItemVOList.get(index), index));
        }
        shelf.setDealList(dealList);
        return shelf;
    }

    private LiXiangMapShelfDealDTO buildLiXiangMapShelfDealDTO(DzItemVO dzItemVO, int index) {
        LiXiangMapShelfDealDTO shelfDealDTO = new LiXiangMapShelfDealDTO();
        shelfDealDTO.setItemId(dzItemVO.getItemIdL());
        shelfDealDTO.setTitle(dzItemVO.getTitle());
        if (dzItemVO.getPic() != null && dzItemVO.getPic().getPic() != null) {
            shelfDealDTO.setPicUrl(dzItemVO.getPic().getPic().getPicUrl());
        }
        shelfDealDTO.setMarketPrice(convertStrPriceYuan2IntCent(dzItemVO.getMarketPrice()));
        shelfDealDTO.setSalePrice(convertStrPriceYuan2IntCent(dzItemVO.getSalePrice()));
        if (CollectionUtils.isNotEmpty(dzItemVO.getPriceBottomTags())) {
            DzTagVO dzPromoVO = dzItemVO.getPriceBottomTags().get(0);
            shelfDealDTO.setPromoTag(dzPromoVO.getName());
            shelfDealDTO.setDealPromos(buildLiXiangDealPromoDetail(dzPromoVO.getPromoDetail()));
        }
        shelfDealDTO.setDiscountTag(getDiscountTag(dzItemVO));
        shelfDealDTO.setSale(dzItemVO.getSale());
        shelfDealDTO.setIndex(index);
        shelfDealDTO.setProductTags(dzItemVO.getProductTags());
        shelfDealDTO.setActivityTag(getActivityTag(dzItemVO));
        shelfDealDTO.setJumpUrl(dzItemVO.getJumpUrl());
        return shelfDealDTO;
    }

    private LiXiangMapActivityTagDTO getActivityTag(DzItemVO dzItemVO) {
        if (dzItemVO.getPic() != null && CollectionUtils.isNotEmpty(dzItemVO.getPic().getFloatTags())) {
            return getActivityTag(dzItemVO.getPic().getFloatTags().get(0));
        }
        if (dzItemVO.getPreTitleTag() != null) {
            return getActivityTag(dzItemVO.getPreTitleTag());
        }
        return null;
    }

    private LiXiangMapActivityTagDTO getActivityTag(FloatTagVO floatTag) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.gateways.rpc.DealShelfOpenApiRPCService.getActivityTag(com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO)");
        if (floatTag == null) {
            return null;
        }
        LiXiangMapActivityTagDTO activityTagDTO = new LiXiangMapActivityTagDTO();
        if (floatTag.getIcon() != null && StringUtils.isNotBlank(floatTag.getIcon().getPicUrl())) {
            activityTagDTO.setPicUrl(floatTag.getIcon().getPicUrl());
            return activityTagDTO;
        }
        if (floatTag.getLabel() == null || StringUtils.isBlank(floatTag.getLabel().getText())) {
            return null;
        }
        activityTagDTO.setLabel(getText(floatTag.getLabel().getText()));
        return activityTagDTO;
    }

    private String getText(String text) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.gateways.rpc.DealShelfOpenApiRPCService.getText(java.lang.String)");
        if (StringUtils.isBlank(text)) {
            return null;
        }
        if (text.trim().startsWith(JSON_START)) {
            return readTextFromRichLabels(text.trim());
        }
        return text.trim();
    }

    private String readTextFromRichLabels(String richLabelsJson) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.gateways.rpc.DealShelfOpenApiRPCService.readTextFromRichLabels(java.lang.String)");
        List<RichLabel> richLabels = JsonCodec.decode(richLabelsJson, new TypeReference<List<RichLabel>>() {});
        if (CollectionUtils.isEmpty(richLabels)) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (RichLabel richLabel : richLabels) {
            stringBuilder.append(richLabel.getText());
        }
        return stringBuilder.toString();
    }

    private String getDiscountTag(DzItemVO dzItemVO) {
        if (CollectionUtils.isEmpty(dzItemVO.getPromo())) {
            return null;
        }
        return dzItemVO.getPromo().get(0).getName();
    }

    private List<LiXiangMapDealPromoDetailDTO> buildLiXiangDealPromoDetail(DzPromoDetailVO detail) {
        if (detail == null) {
            return null;
        }
        List<LiXiangMapDealPromoDetailDTO> result = new ArrayList<>();
        for (DzPromoPerItemVO promoItem : detail.getPromoItems()) {
            LiXiangMapDealPromoDetailDTO itemDTO = new LiXiangMapDealPromoDetailDTO();
            itemDTO.setTitle(promoItem.getTitle());
            itemDTO.setDesc(promoItem.getDesc());
            itemDTO.setPromoPrice(promoItem.getPromoPrice());
            result.add(itemDTO);
        }
        return result;
    }

    private ActivityRequest buildActivityRequestOfLiXiangMap(long mtShopId) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, SCENE_CODE_LI_XIANG);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, VCClientTypeEnum.MT_APP.getCode());
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "");
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, MT_APP_VERSION);
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, Long.valueOf(mtShopId).intValue());
        activityRequest.addParam(ShelfActivityConstants.Params.mtPoiIdL, mtShopId);
        activityRequest.addParam("spaceKey", DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY);
        return activityRequest;
    }

    private ActivityRequest buildActivityRequestOfBaiduMap(ShopDTO shopDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.gateways.rpc.DealShelfOpenApiRPCService.buildActivityRequestOfBaiduMap(com.dianping.shopremote.remote.dto.ShopDTO)");
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, SCENE_CODE_BAIDU);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, BAI_DU_MAP_CLIENT_TYPE);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "");
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, shopDTO.getCityId() == null ? 0 : shopDTO.getCityId().intValue());
        activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopDTO.getShopId());
        //这里就塞 ctxShop 是防止引擎内部再次调用，放大流量
        activityRequest.addParam(ShelfActivityConstants.Ctx.ctxShop, new ShopM(shopDTO));
        activityRequest.addParam("spaceKey", DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY);
        return activityRequest;
    }

    private BaiduMapShelfDTO buildBaiduMapShelfDTO(List<DzItemVO> dzItemVOList, long dpPoiId) {
        BaiduMapShelfDTO shelf = new BaiduMapShelfDTO();
        List<BaiduMapShelfDealDTO> dealList = new ArrayList<>(dzItemVOList.size());
        for (int index = 0; index < dzItemVOList.size(); index++) {
            dealList.add(buildBaiduMapShelfDealDTO(dzItemVOList.get(index), index, dpPoiId));
        }
        shelf.setDealList(dealList);
        return shelf;
    }

    private BaiduMapShelfDealDTO buildBaiduMapShelfDealDTO(DzItemVO dzItemVO, int index, long dpPoiId) {
        BaiduMapShelfDealDTO shelfDealDTO = new BaiduMapShelfDealDTO();
        shelfDealDTO.setItemId(dzItemVO.getItemIdL());
        shelfDealDTO.setTitle(dzItemVO.getTitle());
        if (dzItemVO.getPic() != null && dzItemVO.getPic().getPic() != null) {
            shelfDealDTO.setPicUrl(dzItemVO.getPic().getPic().getPicUrl());
        }
        shelfDealDTO.setMarketPrice(convertStrPriceYuan2IntCent(dzItemVO.getMarketPrice()));
        shelfDealDTO.setSalePrice(convertStrPriceYuan2IntCent(dzItemVO.getSalePrice()));
        shelfDealDTO.setBasePrice(convertStrPriceYuan2IntCent(dzItemVO.getBasePrice()));
        if (CollectionUtils.isNotEmpty(dzItemVO.getPromo())) {
            DzPromoVO dzPromoVO = dzItemVO.getPromo().get(0);
            shelfDealDTO.setPromoTag(dzPromoVO.getName());
            shelfDealDTO.setDealPromos(buildDealPromoDetail(sumConflictPromo(dzPromoVO.getDetail())));
        }
        shelfDealDTO.setSale(dzItemVO.getSale());
        shelfDealDTO.setJumpUrl(dzItemVO.getJumpUrl());
        if(CollectionUtils.isNotEmpty(shopIdList) && shopIdList.contains(dpPoiId)) {
            shelfDealDTO.setJumpUrl(dzItemVO.getJumpUrl() + "&pagesource=baidumap");
        }
        shelfDealDTO.setIndex(index);
        shelfDealDTO.setProductTags(dzItemVO.getProductTags());
        //填充来源于扩展的数据
        paddingBaiduMapShelfDealFromExtra(shelfDealDTO, dzItemVO.getExtra());
        return shelfDealDTO;
    }

    private List<DealPromoDetailDTO> buildDealPromoDetail(int conflictPromSum) {
        if(conflictPromSum <= 0) {
            return Lists.newArrayList();
        }
        List<DealPromoDetailDTO> dealPromos = Lists.newArrayList();
        DealPromoDetailDTO dealPromoDetailDTO = new DealPromoDetailDTO();
        dealPromoDetailDTO.setPromoPrice(conflictPromSum);
        dealPromoDetailDTO.setPromoType(1);
        dealPromos.add(dealPromoDetailDTO);
        return dealPromos;
    }

    private int sumConflictPromo(DzPromoDetailVO dzPromoDetailVO) {
        if(dzPromoDetailVO == null || CollectionUtils.isEmpty(dzPromoDetailVO.getPromoItems())) {
            return 0;
        }
        return dzPromoDetailVO.getPromoItems().stream()
                .filter(promo -> promo != null && StringUtils.isNotEmpty(promo.getPromoPrice()))
                .mapToInt(promo -> convertStrPriceYuan2IntCent(promo.getPromoPrice())).sum();
    }

    private void paddingBaiduMapShelfDealFromExtra(BaiduMapShelfDealDTO shelfDealDTO, String extra) {
        if (StringUtils.isEmpty(extra)) {
            return;
        }
        Map<String, Object> extraMap = JsonCodec.decode(extra, new TypeReference<Map<String, Object>>() {});
        if (MapUtils.isEmpty(extraMap)) {
            return;
        }
        //填充团单分类
        if (extraMap.containsKey(PaddingJsonItemExtraOpt.DEAL_CATEGORY_ID_KEY)) {
            shelfDealDTO.setCategoryId(ParamsUtil.getIntSafely(extraMap, PaddingJsonItemExtraOpt.DEAL_CATEGORY_ID_KEY));
        }
    }

    private int convertStrPriceYuan2IntCent(String price) {
        if (StringUtils.isEmpty(price)) {
            return 0;
        }
        String priceNum = price.contains("-") ? price.substring(2) : price;
        return new BigDecimal(priceNum).multiply(BigDecimal.valueOf(100)).intValue();
    }


    /**
     * @param dpPoiId
     * @return 根据 PoiId 查询店铺信息，当前用于查询所在的城市Id，以查询分城市销量
     */
    private ShopDTO loadShop(long dpPoiId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.gateways.rpc.DealShelfOpenApiRPCService.loadShop(long)");
        CompletableFuture<ShopDTO> shopFuture = compositeAtomService.loadShop((int)dpPoiId);
        try {
            return shopFuture.get(100, TimeUnit.MILLISECONDS);
        } catch (Exception ex) {
            return null;
        }
    }

    private ShelfComponentDTO dzShelfResponseVO2ShelfComponentDTO(DzShelfResponseVO shelfResponseVO) {
        //check and get
        ProductAreaComponentVO productAreaComponentVO = extractProductAreaFromResp(shelfResponseVO);
        if (productAreaComponentVO == null || productAreaComponentVO.getItemArea() == null) {
            return null;
        }
        List<DzItemVO> dzItemVOS = productAreaComponentVO.getItemArea().getProductItems();
        if (CollectionUtils.isEmpty(dzItemVOS)) {
            return null;
        }
        //convert
        ShelfComponentDTO shelfComponentDTO = new ShelfComponentDTO();
        shelfComponentDTO.setFoldTitle(productAreaComponentVO.getMore().getText());
        shelfComponentDTO.setFoldThreshold(productAreaComponentVO.getItemArea().getDefaultShowNum());
        shelfComponentDTO.setDealTotal(dzItemVOS.size());
        List<ShelfDealDTO> dealList = dzItemVOS.stream().map(this::convertDzItemVO2ShelfDealDTO).collect(Collectors.toList());
        shelfComponentDTO.setDealList(dealList);
        return shelfComponentDTO;
    }

    private ShelfDealDTO convertDzItemVO2ShelfDealDTO(DzItemVO dzItemVO) {
        ShelfDealDTO shelfDealDTO = new ShelfDealDTO();
        shelfDealDTO.setItemId(dzItemVO.getItemId());
        shelfDealDTO.setItemIdL(dzItemVO.getItemIdL());
        shelfDealDTO.setTitle(dzItemVO.getTitle());
        shelfDealDTO.setSalePrice(dzItemVO.getSalePrice());
        shelfDealDTO.setMarketPrice(dzItemVO.getMarketPrice());
        shelfDealDTO.setJumpUrl(String.format(JUMP_URL_FORMAT, dzItemVO.getItemIdL()));
        shelfDealDTO.setSale(dzItemVO.getSale());
        if (dzItemVO.getPic() != null && dzItemVO.getPic().getPic() != null) {
            shelfDealDTO.setPicUrl(dzItemVO.getPic().getPic().getPicUrl());
        }
        if (CollectionUtils.isNotEmpty(dzItemVO.getPromo())) {
            shelfDealDTO.setPromoTag(dzItemVO.getPromo().get(0).getName());
        }
        shelfDealDTO.setPinPriceTag(getPinPriceTagFromDzItem(dzItemVO));
        return shelfDealDTO;
    }

    /**
     * 获取拼团标签
     *
     * @param dzItemVO
     * @return
     */
    private String getPinPriceTagFromDzItem(DzItemVO dzItemVO) {
        if (CollectionUtils.isEmpty(dzItemVO.getBottomTags())) {
            return null;
        }
        for (RichLabelVO bottomTag : dzItemVO.getBottomTags()) {
            if (StringUtils.isEmpty(bottomTag.getText())) {
                continue;
            }
            List<LifeDealFloorsBuilderExt.RichLabel> richLabelList = JsonCodec.decode(bottomTag.getText(), new TypeReference<List<LifeDealFloorsBuilderExt.RichLabel>>() {
            });
            if (CollectionUtils.isEmpty(richLabelList) || richLabelList.size() < 2) {
                return null;
            }
            //x人团
            String textTuan = richLabelList.get(0).getText();
            //x元
            String textYuan = richLabelList.get(1).getText();
            if (!textTuan.contains("团")) {
                continue;
            }
            return textTuan + textYuan;
        }
        return null;
    }


    private ProductAreaComponentVO extractProductAreaFromResp(DzShelfResponseVO dzShelfResponseVO) {
        if (dzShelfResponseVO == null || dzShelfResponseVO.getShelfComponent() == null || CollectionUtils.isEmpty(dzShelfResponseVO.getShelfComponent().getFilterIdAndProductAreas())) {
            return null;
        }
        FilterBtnIdAndProAreasVO filterArea = dzShelfResponseVO.getShelfComponent().getFilterIdAndProductAreas().get(0);
        return CollectionUtils.isEmpty(filterArea.getProductAreas()) ? null : filterArea.getProductAreas().get(0);
    }

    private List<DzItemVO> extractDealsFromResp(DzShelfResponseVO dzShelfResponseVO) {
        ProductAreaComponentVO productAreaComponentVO = extractProductAreaFromResp(dzShelfResponseVO);
        if (productAreaComponentVO == null || productAreaComponentVO.getItemArea() == null) {
            return new ArrayList<>();
        }
        return productAreaComponentVO.getItemArea().getProductItems();
    }

    private ActivityRequest buildActivityRequest(Long mtShopId) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, SCENE_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, PLATFORM);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "");
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, MT_APP_VERSION);
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, mtShopId.intValue()); // poiMigrate
        activityRequest.addParam(ShelfActivityConstants.Params.mtPoiIdL, mtShopId);
        activityRequest.addParam("spaceKey", DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY);
        return activityRequest;
    }

    private <T> OpenApiResponse<T> getDefaultSuccessResponse(T shelfComponentDTO) {
        OpenApiResponse<T> openApiResponse = new OpenApiResponse<>();
        openApiResponse.setSuccess();
        openApiResponse.setData(shelfComponentDTO);
        return openApiResponse;
    }

    @Data
    public static class RichLabel {
        private String text;
    }
}
