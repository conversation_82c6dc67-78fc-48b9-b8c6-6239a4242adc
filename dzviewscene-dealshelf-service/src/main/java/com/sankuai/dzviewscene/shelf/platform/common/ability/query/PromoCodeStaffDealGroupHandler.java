package com.sankuai.dzviewscene.shelf.platform.common.ability.query;

import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityDTO;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityRequest;
import com.dianping.gmkt.event.api.distribution.enums.DistributorActivityType;
import com.dianping.gmkt.event.api.distribution.enums.DistributorActivityUserType;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.technician.common.api.domain.Distribution;
import com.dianping.technician.common.api.enums.MobileOSEnum;
import com.dianping.technician.common.api.enums.PlatformEnum;
import com.dianping.technician.common.api.enums.SourceEnum;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.technician.trade.api.product.dto.ShelfProductCategoryModel;
import com.sankuai.technician.trade.api.product.dto.ShelfProductItem;
import com.sankuai.technician.trade.api.product.request.EnvironmentContext;
import com.sankuai.technician.trade.api.product.request.TechProductShelfRequest;
import com.sankuai.technician.trade.common.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.enums.ProductTypeEnum.*;
import static com.sankuai.technician.trade.api.product.enums.ShelfSceneEnum.BEAUTY_COMMON_DEAL_SHELF;

/**
 * @description : 用于职人绑定货架团单召回
 * @date : 2024/9/2
 */
@Slf4j
@Component
public class PromoCodeStaffDealGroupHandler implements GroupQueryHandler {
    @Resource
    private CompositeAtomService compositeAtomService;
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.promocode.staff.use.activity.param", defaultValue = "false")
    private boolean useActivityParam;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName, Map<String, Object> params) {
        try {
            String codeId = activityContext.getParam(ShelfActivityConstants.Params.entityId);
            if (StringUtils.isBlank(codeId) && !StringUtils.isNumeric(codeId)) {
                return CompletableFuture.completedFuture(null);
            }
            CompletableFuture<PromoQRCodeResponse<StaffCodeDTO>> response = compositeAtomService.getStaffCodeDTOByCodeId(
                    Long.parseLong(activityContext.getParam(ShelfActivityConstants.Params.entityId)));
            StaffCodeDTO staffCodeDTO = Optional.ofNullable(response.get(1, TimeUnit.SECONDS)).map(PromoQRCodeResponse::getData).orElse(null);
            if (staffCodeDTO == null || staffCodeDTO.getTechnicianId() == null || staffCodeDTO.getTechnicianId() <= 0) {
                log.error("queryStaffBindShelfInfo queryProductShelf getStaffCodeDTOByCodeId error, staffCodeId: {}, resp: {}", Long.parseLong(activityContext.getParam(ShelfActivityConstants.Params.entityId)), response);
                return CompletableFuture.completedFuture(null);
            }
            addActivityContextExtraParams(activityContext, staffCodeDTO);

            //异步查询对应的货架商品
            return getDistributorActivityIdFuture(staffCodeDTO).
                    thenApply(distributorActivityId -> buildShelfRequest(activityContext, staffCodeDTO, BEAUTY_COMMON_DEAL_SHELF.getSceneCode(), distributorActivityId))
                    .thenCompose(dealRequest -> compositeAtomService.queryStaffShelf(dealRequest))
                    .thenApply(future -> {
                        if (future != null && future.respSuccess() && future.getData() != null) {
                            //转换为productGroupM包装对象
                            List<ShelfProductItem> dealList = future.getData().getTechProductCategoryModels().stream().filter(Objects::nonNull)
                                    .map(ShelfProductCategoryModel::getShelfProductItems)
                                    .filter(Objects::nonNull)
                                    .flatMap(Collection::stream)
                                    .collect(Collectors.toList());
                            return convert2ProductGroupM(dealList, Lists.newArrayList(getShopIdFromParams(activityContext)));
                        }
                        return null;
                    }).exceptionally(e -> {
                        log.error("PromoCodeStaffDealGroupHandler.queryStaffShelf error, sceneCode: {},codeId:{}", activityContext.getSceneCode(),
                                Long.parseLong(activityContext.getParam(ShelfActivityConstants.Params.entityId)), e);
                        return null;
                    });
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityContext, e);
            log.error("PromoCodeStaffDealGroupHandler.query error, sceneCode: {},codeId:{}", activityContext.getSceneCode(),
                    Long.parseLong(activityContext.getParam(ShelfActivityConstants.Params.entityId)), e);
        }
        return null;
    }

    private CompletableFuture<String> getDistributorActivityIdFuture(StaffCodeDTO staffCodeDTO) {
        if (!useActivityParam) {
            return CompletableFuture.completedFuture("0");
        }
        CompletableFuture<String> distributorActivityIdFuture;
        if (!staffCodeDTO.getBindTechnician()) {
            distributorActivityIdFuture = CompletableFuture.completedFuture("0");
        } else {
            distributorActivityIdFuture = compositeAtomService.queryJoinDistributorActivity(buildDistributorActivityRequest(staffCodeDTO))
                    .thenApply(resp -> {
                        log.info("distributorActivityService.queryJoinDistributorActivity technicianId = {}, resp = {}.", staffCodeDTO.getTechnicianId(), resp);
                        List<DistributorActivityDTO> distributorActivityDTOS = Optional.ofNullable(resp).map(com.dianping.gmkt.event.api.model.CommonResponse::getData).orElse(null);
                        if (CollectionUtils.isEmpty(distributorActivityDTOS)) {
                            return "0";
                        }
                        return distributorActivityDTOS.stream().findFirst().map(DistributorActivityDTO::getActivityRecordId).map(String::valueOf).orElse("0");
                    });
        }
        return distributorActivityIdFuture;
    }

    private void addActivityContextExtraParams(ActivityContext activityContext, StaffCodeDTO staffCodeDTO) {
        // 添加手艺人ID到活动上下文中entityId， 原来的entityId（职人码ID)没有用了
        Map<String, Object> extraMap;
        if (activityContext.getParam(ShelfActivityConstants.Params.extra) == null) {
            extraMap = new HashMap<>();
        } else {
            extraMap = JsonCodec.decode((String) activityContext.getParam(ShelfActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {
            });
        }
        extraMap.put("techId", staffCodeDTO.getTechnicianId());
        activityContext.addParam(ShelfActivityConstants.Params.extra, JsonCodec.encode(extraMap));
    }

    private ProductGroupM convert2ProductGroupM(List<ShelfProductItem> dealList, List<Long> shopIds) {
        if (CollectionUtils.isEmpty(dealList)) {
            return new ProductGroupM();
        }
        List<ProductM> products = dealList.stream().map(dealId -> buildProductM(dealId, shopIds)).collect(Collectors.toList());
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(products);
        productGroupM.setTotal(CollectionUtils.isEmpty(products) ? 0 : products.size());
        return productGroupM;
    }

    private ProductM buildProductM(ShelfProductItem dealId, List<Long> shopIds) {
        ProductM productM = new ProductM();
        productM.setProductId(Integer.parseInt(dealId.getProductId()));
        productM.setProductType(convertProductMProductType(dealId.getProductType()));
        productM.setShopIds(shopIds.stream().map(Long::intValue).collect(Collectors.toList()));
        productM.setShopLongIds(shopIds);
        return productM;
    }

    /**
     * 将货架的商品类型转换为平台商品类型
     * @param productType 货架的商品类型，参考：{@link ProductTypeEnum}
     */
    private int convertProductMProductType(int productType) {
        switch (productType) {
            case 1:
                return TIME_CARD.getType();
            case 2:
            case 7:
                return DEAL.getType();
            case 3:
                return GENERAL_SPU.getType();
            default:
                return UNKNOWN.getType();
        }
    }


    private long getShopIdFromParams(ActivityContext activityContext) {
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    @NotNull
    private TechProductShelfRequest buildShelfRequest(ActivityContext activityContext, StaffCodeDTO staffCodeDTO, String sceneCode, String distributorActivityId) {
        TechProductShelfRequest request = new TechProductShelfRequest();
        request.setScene(sceneCode);
        request.setTechId(Math.toIntExact(staffCodeDTO.getTechnicianId()));
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        request.setEnvironmentContext(EnvironmentContext
                .builder().userId(PlatformUtil.isMT(platform) ? activityContext.getParam(ShelfActivityConstants.Params.mtUserId) : activityContext.getParam(ShelfActivityConstants.Params.dpUserId))
                .inApp(PlatformUtil.isApp(platform))
                .source(convertQrClientType(activityContext))
                .lat(activityContext.getParam(ShelfActivityConstants.Params.lat)).lng(activityContext.getParam(ShelfActivityConstants.Params.lng))
                .uuid(activityContext.getParam(ShelfActivityConstants.Params.deviceId))
                .mobileOS(MobileOSEnum.fromCode(
                        activityContext.getParam(ShelfActivityConstants.Params.clientType) != null
                                && "android".equals(activityContext.getParam(ShelfActivityConstants.Params.clientType)) ?
                                MobileOSEnum.ANDROID.getCode() : MobileOSEnum.UNKNOWN.getCode()))
                .cityId(PlatformUtil.isMT(platform) ? activityContext.getParam(ShelfActivityConstants.Params.mtCityId) : activityContext.getParam(ShelfActivityConstants.Params.dpCityId))
                .platform(PlatformUtil.isMT(platform) ? PlatformEnum.MEI_TUAN.getCode() : PlatformEnum.DIAN_PING.getCode()).build());

        Map<String, String> extParams = new HashMap<>();
        extParams.put("discountCode", activityContext.getParam(ShelfActivityConstants.Params.entityId));
        extParams.put("discountActivityId", distributorActivityId);
        Distribution distribution = Distribution.builder().channel("discount_code").extInfo(extParams).build();
        request.setDistribution(distribution);
        return request;
    }

    private DistributorActivityRequest buildDistributorActivityRequest(StaffCodeDTO staffCodeDTO) {
        DistributorActivityRequest request = new DistributorActivityRequest();
        request.setActivityType(DistributorActivityType.DISTRIBUTOR_SHARE_EARN.getCode());
        request.setUserType(DistributorActivityUserType.TECHNICIAN.getCode());
        request.setUserId(staffCodeDTO.getTechnicianId());
        return request;
    }

    private SourceEnum convertQrClientType(ActivityContext activityContext) {
        int userAgent = activityContext.getParam(ShelfActivityConstants.Params.userAgent);
        if (userAgent == VCClientTypeEnum.DP_APP.getCode()) {
            return SourceEnum.DP_APP;
        } else if (userAgent == VCClientTypeEnum.MT_APP.getCode()) {
            return SourceEnum.MT_APP;
        } else if (userAgent == VCClientTypeEnum.MT_WX.getCode()) {
            return SourceEnum.MT_WX_APP;
        } else {
            return SourceEnum.MT_PC;
        }
    }
}
