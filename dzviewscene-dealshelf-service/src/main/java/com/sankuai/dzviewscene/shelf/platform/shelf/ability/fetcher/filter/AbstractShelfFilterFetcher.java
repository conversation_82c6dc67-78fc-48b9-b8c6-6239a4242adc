package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.Platform;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.product.shelf.common.dto.*;
import com.dianping.product.shelf.common.dto.activity.ActivityNavStyleDTO;
import com.dianping.product.shelf.common.dto.featureoption.NavTagOption;
import com.dianping.product.shelf.common.enums.NavTagTypeEnum;
import com.dianping.product.shelf.common.enums.ShelfTypeEnum;
import com.dianping.product.shelf.common.request.ShelfRequest;
import com.dianping.product.shelf.common.request.ShelfRequestAttrMapKeys;
import com.dianping.product.shelf.common.utils.ExtendDataDtoGsonUtil;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.medicalbeauty.ProductPlatformUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.ShelfCacheGreyConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.ShelfSceneCacheConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.BPShelfRequestBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfFilterUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public abstract class AbstractShelfFilterFetcher extends FilterFetcher {

    /**
     * 商品平台返回的货架标签（随时退、过期退等）
     */
    public static final String SHELF_TAGS = "shelfTagsFromPlatform";


    /*************************** 缓存超时+刷新配置 **************************/
    /**
     * 平台筛选缓存过期时间
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformfilter.cache.expire.time", defaultValue = "86400")
    private int platformFilterCacheExpireTime;

    /**
     * 平台筛选缓存刷新时间
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformfilter.cache.refresh.time", defaultValue = "60")
    private int platformFilterCacheRefreshTime;

    /**
     * 平台筛选缓存总开关
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformfilter.cache.switch", defaultValue = "false")
    private boolean filterCacheSwitch;

    /**
     * 平台筛选灰度策略
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformfilter.cache.grey.config")
    private ShelfCacheGreyConfig filterGreyConfig;


    private static final String REDIS_FILTER_CATEGORY = "dzp_plat_filter";

    private static CacheClientConfig cacheClientConfig = new CacheClientConfig("redis-vc", 100);

    private static TypeReference<ProductShelf> filterCacheTypeReference = new TypeReference<ProductShelf>() {
    };


    @Resource
    private AtomFacadeService atomFacadeService;

    @Resource
    private WholePlatformShelfFilterFetcher wholePlatformShelfFilterFetcher;

    @Override
    public CompletableFuture<Map<String, FilterM>> build(ActivityContext ctx) {
        Map<String, String> filterComponent2Group = ctx.getParam(Params.filterComponent2Group);
        if (MapUtils.isEmpty(filterComponent2Group)) {
            throw new BusinessException("需要传入filterComponent2Group参数指定filter组件ID和商品组名映射关系");
        }

        // 1. 构造参数
        ShelfRequest shelfRequest = buildShelfRequest(ctx);

        // 2. 查询导航ID和导航Map
        CompletableFuture<Map<String, FilterM>> componentId2FilterCompletableFuture = getShelfFuture(shelfRequest, ctx.getSceneCode(), filterComponent2Group)
                .thenApply(this::buildComponentId2Filter);

        // 3. 转换为商品组合导航映射
        CompletableFuture<Map<String, FilterM>> filterMsCompletableFuture = componentId2FilterCompletableFuture
                .thenApply(componentId2Filter -> buildProductGroup2Filter(filterComponent2Group, componentId2Filter));

        // 4. 设置选中状态
        return filterMsCompletableFuture
                .thenApply(filterMs -> buildSelectedFilter(ctx, filterMs))
                .thenApply(filterMs -> postProcessFilters(ctx, filterMs))
                .thenCompose(filterMs -> {
                    if (isFShelf(ctx, filterMs) && hitExp(ctx)) {
                        ctx.addParam(ShelfActivityConstants.Params.isFShelf, true);
                        return wholePlatformShelfFilterFetcher.build(ctx);
                    }
                    return CompletableFuture.completedFuture(filterMs);
                });
    }

    private boolean hitExp(ActivityContext ctx) {
        Map<String, Object> shelfTypeConfig = ctx.getParam(QueryFetcher.Params.recallFShelfConfig);
        if (MapUtils.isEmpty(shelfTypeConfig)) {
            return false;
        }
        List<String> skList = (List<String>) ParamsUtil.getValue(shelfTypeConfig, "skList", Lists.newArrayList());
        return DouHuUtils.hitAnySk(ctx.getParam(ShelfActivityConstants.Params.douHus), skList);
    }

    private boolean isFShelf(ActivityContext ctx, Map<String, FilterM> filterMs) {
        Map<String, Object> recallFShelfConfig = ctx.getParam(QueryFetcher.Params.recallFShelfConfig);
        if (MapUtils.isEmpty(recallFShelfConfig)) {
            return false;
        }
        // 二级类目控制
        ShopCategoryConfig backCategory2Cfg = JSON.parseObject(JSON.toJSONString(ParamsUtil.getValue(recallFShelfConfig, "backCategory2Cfg", new ShopCategoryConfig())), ShopCategoryConfig.class);
        Boolean hitConfig = ShopCategoryConfigUtils.getHitConfig(ActivityCtxtUtils.toActivityCtx(ctx), backCategory2Cfg, boolean.class);
        if (!Boolean.TRUE.equals(hitConfig)) {
            return false;
        }
        int productNumThreshold = ParamsUtil.getIntSafely(recallFShelfConfig, "productNumThreshold");
        int tabNumThreshold = ParamsUtil.getIntSafely(recallFShelfConfig, "tabNumThreshold");
        if (productNumThreshold <= 0 && tabNumThreshold <= 0) {
            return false;
        }
        int totalProductNum = getTotalProductNum(filterMs);
        int mainTabNum = getMainTabNum(ctx, filterMs);
        int platform = NumberUtils.toInt(ctx.getParam(ShelfActivityConstants.Params.platform) + "", 1);
        long userId = PlatformUtil.isMT(platform) ? NumberUtils.toLong(ctx.getParam(ShelfActivityConstants.Params.mtUserId) + "", 0)
                : NumberUtils.toLong(ctx.getParam(ShelfActivityConstants.Params.dpUserId) + "", 0);
        LogUtils.recordKeyMsg(userId, "Judge F-shelf Info", String.format("totalProductNum: %s, mainTabNum: %s", totalProductNum, mainTabNum));
        return totalProductNum >= productNumThreshold && mainTabNum >= tabNumThreshold;
    }

    private int getMainTabNum(ActivityContext ctx, Map<String, FilterM> filterMs) {
        List<String> groupNames = ctx.getParam(QueryFetcher.Params.groupNames);
        if (CollectionUtils.isEmpty(groupNames)) {
            return 0;
        }
        // 只考虑一组商品#团购
        String groupName = groupNames.get(0);
        if (Objects.nonNull(filterMs.get(groupName)) && CollectionUtils.isNotEmpty(filterMs.get(groupName).getFilters())) {
            return filterMs.get(groupName).getFilters().stream()
                    .filter(btn -> NavTagTypeEnum.isProjectCateNavTag(btn.getNavTagType())).mapToInt(e->1).sum();
        }
        return 0;
    }

    private int getTotalProductNum(Map<String, FilterM> filterMs) {
        return filterMs.values().stream().filter(Objects::nonNull).map(FilterM::getProductTotalCount).findFirst().orElse(0);
    }

    private Map<String, FilterM> buildSelectedFilter(ActivityContext ctx, Map<String, FilterM> filterMs) {
        if (MapUtils.isEmpty(filterMs)) {
            return Maps.newHashMap();
        }
        filterMs.entrySet()
                .stream()
                .filter(filterMEntry -> CollectionUtils.isNotEmpty(filterMEntry.getValue().getFilters()))
                .forEach(filterEntry -> {
                    // 1. 计算选中标签
                    long selectedTagId = getSelected(filterEntry.getKey(), filterEntry.getValue(), ctx);

                    // 2. 设置选中状态
                    selectedTagId = setFilterSelectedStatus(selectedTagId, null, filterEntry.getValue().getFilters());

                    // 3. 设置选中标签参数, 非多组商品的情况下, 才会重新设置选中标签
                    if (selectedTagId > 0) {
                        ctx.addParam(ShelfActivityConstants.Params.selectedFilterId, selectedTagId);
                    }
                    // 4. 设置筛选器选中标签
                    String filterparams = ctx.getParam(ShelfActivityConstants.Params.selectedFilterParams);
                    if(StringUtils.isEmpty(filterparams)){
                        String summaryProductIds = ctx.getParam(ShelfActivityConstants.Params.summaryProductIds);
                        List<String> secondaryFilter = ParamUtil.extractBySummarypids(summaryProductIds, "secondaryFilter");
                        List<String> filterId = ParamUtil.extractBySummarypids(summaryProductIds, "filterId");
                        ShelfFilterUtils.setSecondaryFilterActivityContext(filterId, secondaryFilter, filterEntry.getValue().getFilters(), ctx);
                    }
                });

        return filterMs;
    }

    private Map<String, FilterM> buildProductGroup2Filter(Map<String, String> filterComponent2Group, Map<String, FilterM> componentId2Filter) {
        if (MapUtils.isEmpty(componentId2Filter)) {
            return Maps.newHashMap();
        }
        return componentId2Filter.entrySet()
                .stream()
                .collect(
                        HashMap::new,
                        (map, componentId2FilterEntry) -> {
                            String groupName = findGroupName(componentId2FilterEntry.getKey(), filterComponent2Group);
                            if (StringUtils.isBlank(groupName)) return;
                            map.put(groupName, componentId2FilterEntry.getValue());
                        },
                        HashMap::putAll
                );
    }

    private Map<String, FilterM> buildComponentId2Filter(ProductShelf shelf) {
        if (shelf == null || CollectionUtils.isEmpty(shelf.getShelfComponents())) {
            return null;
        }

        return shelf
                .getShelfComponents()
                .stream()
                .filter(Objects::nonNull)
                .collect(HashMap::new,
                        (map, shelfComponent) -> {
                            FilterM filterM = buildFilterM(shelfComponent);
                            map.put(String.valueOf(shelfComponent.getGroupId()), filterM);
                            // 由于同一个类目下货架的Id可能不同，所以兼容使用name做筛选
                            map.put(shelfComponent.getName(), filterM);
                        },
                        HashMap::putAll);
    }


    private String findGroupName(String componentId, Map<String, String> component2Group) {
        if (component2Group.get(componentId) == null) {
            return null;
        }
        return component2Group.get(componentId);
    }

    protected long getSelected(String groupName, FilterM filterM, ActivityContext activityContext) {
        // 1. 明确知道筛选ID, 则优先级最高
        long selectedTagId = NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.selectedFilterId) + "", 0);
        if (selectedTagId > 0) {
            return selectedTagId;
        }
        // 2. 否则调研业务实现扩展点计算当前选中ID, 用于关键词匹配场景
        return findExtPoint(activityContext, FilterFetcherExt.EXT_POINT_FILTER_CODE).selected(activityContext, groupName, filterM);
    }

    // 当前只设置叶子节点为选中状态
    private long setFilterSelectedStatus(long selectedTagId, FilterBtnM parentFilterBtnM, List<FilterBtnM> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return 0L;
        }
        long selectId = selectedTagId;
        // 1. 根据入参设置选中状态
        if (!setSelectedBySelectedTagId(selectedTagId, parentFilterBtnM, filters)) {
            // 2、如果设置失败, 默认选中第一个
            filters.get(0).setSelected(true);
            selectId = filters.get(0).getFilterId();
            if (CollectionUtils.isNotEmpty(filters.get(0).getChildren())) {
                filters.get(0).getChildren().get(0).setSelected(true);
                selectId = filters.get(0).getChildren().get(0).getFilterId();
            }
        } else {
            selectId = selectedTagId;
        }
        return selectId;
    }

    private boolean setSelectedBySelectedTagId(long selectedTagId, FilterBtnM parentFilterBtnM, List<FilterBtnM> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return false;
        }
        for (FilterBtnM filterBtnM : filters) {
            if (CollectionUtils.isNotEmpty(filterBtnM.getChildren()) && setSelectedBySelectedTagId(selectedTagId, filterBtnM, filterBtnM.getChildren())) {
                return true;
            }
            if (selectedTagId == filterBtnM.getFilterId()) {
                filterBtnM.setSelected(true);
                if (parentFilterBtnM != null) {
                    parentFilterBtnM.setSelected(true);
                }
                return true;
            }
        }
        return false;
    }

    private FilterM buildFilterM(FilterGroup filterGroup) {
        FilterM filterM = new FilterM();
        filterM.setScene(filterGroup.getScene());
        filterM.setProductTotalCount(filterGroup.getProductTotalCount());
        if (CollectionUtils.isEmpty(filterGroup.getFilters())) {
            return filterM;
        }
        filterM.setFilters(
                filterGroup.getFilters()
                        .stream()
                        .map(filter -> buildFilterButtonM(filter, 1))
                        .collect(Collectors.toList())
        );
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(SHELF_TAGS, filterGroup.getShelfTags());
        filterM.setExtra(extraMap);
        return filterM;
    }

    private List<FilterBtnM> buildFilterButtonMListFromNavTags(List<ShelfFilter> filters, int depth) {
        if (CollectionUtils.isEmpty(filters)) {
            return Lists.newArrayList();
        }
        return filters
                .stream()
                .map(filter -> buildFilterButtonM(filter, depth))
                .collect(Collectors.toList());
    }

    private FilterBtnM buildFilterButtonM(ShelfFilter filter, int depth) {
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(filter.getFilterId());
        filterBtnM.setTitle(filter.getFilterName());
        filterBtnM.setTotalCount(filter.getTotalCount());
        filterBtnM.setActivity(filter.isActivity());
        filterBtnM.setActivityStyle(filter.getActivityStyle());
        filterBtnM.setDepth(depth);
        filterBtnM.setNavTagType(filter.getNavTagType());
        filterBtnM.setChildren(buildFilterButtonMListFromNavTags(filter.getChildren(), depth + 1));
        if(filter.getFilterOptionTree() != null){
            filterBtnM.setFilterOptionTree(buildFilterOptionM(filter.getFilterId(), filter.getFilterOptionTree()));
        }
        return filterBtnM;
    }

    private List<FilterOptionM> buildFilterOptionMList(long filterId, List<ShelfFilterOption> filterOptions){
        if(CollectionUtils.isEmpty(filterOptions)){
            return Lists.newArrayList();
        }
        return filterOptions.stream().
                map(filterOption -> buildFilterOptionM(filterId, filterOption))
                .collect(Collectors.toList());
    }

    private FilterOptionM buildFilterOptionM(long filterId, ShelfFilterOption filterOption){
        FilterOptionM filterOptionM = new FilterOptionM();
        filterOptionM.setFilterId(filterId);
        filterOptionM.setIdentityName(filterOption.getIdentityName());
        filterOptionM.setShowName(filterOption.getDisplayName());
        filterOptionM.setMultipleSelect(filterOption.isMultipleSelect());
        filterOptionM.setChildren(buildFilterOptionMList(filterId, filterOption.getChildren()));
        return filterOptionM;
    }

    public ShelfRequest buildShelfRequest(ActivityContext activityContext) {
        ShelfRequest shelfRequest = new ShelfRequest();
        shelfRequest.setPlatform(NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.platform) + "", 1));
        if (shelfRequest.getPlatform() == VCPlatformEnum.MT.getType()) {
            shelfRequest.setCityId(NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.mtCityId) + "", 0));
            shelfRequest.setUserId(NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.mtUserId) + "", 0));
        } else {
            shelfRequest.setCityId(NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.dpCityId) + "", 0));
            shelfRequest.setUserId(NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.dpUserId) + "", 0));
        }
        shelfRequest.setClientType(ProductPlatformUtils.getClientType(activityContext.getParam(ShelfActivityConstants.Params.userAgent), activityContext.getParam(ShelfActivityConstants.Params.clientType)));
        shelfRequest.setDpPoiid(PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId));
        shelfRequest.setMtPoiid(PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId));
        shelfRequest.setSceneCode(activityContext.getParam(QueryFetcher.Params.platformSceneCode));
        shelfRequest.setVersion(activityContext.getParam(ShelfActivityConstants.Params.appVersion));
        shelfRequest.setExps(ShelfQueryFetcher.buildExpsOfShelfRequest(activityContext));
        String deviceId = activityContext.getParam(ShelfActivityConstants.Params.deviceId);
        if (PlatformUtil.isMT(shelfRequest.getPlatform())) {
            shelfRequest.setUuid(deviceId);
        } else {
            shelfRequest.setDpId(deviceId);
        }
        shelfRequest.setAttrMap(buildQueryRequestAttr(activityContext));
        BPShelfRequestBuilder.addSceneTypeParam(activityContext, shelfRequest);
        //填充品牌旗舰店的请求
        paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(shelfRequest, activityContext);
        return shelfRequest;
    }

    public void paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(ShelfRequest shelfRequest, ActivityContext activityContext) {
        if (!ShelfQueryFetcher.isFlagshipStoreReq(activityContext.getSceneCode(), activityContext.getParam(ShelfActivityConstants.Params.spaceKey)) || shelfRequest == null) {
            return;
        }
        shelfRequest.setShelfType(ShelfTypeEnum.FLAGSHIP_STORE_SHELF);
        shelfRequest.setStoreId(getFlagshipStoreIdByStoreUUID(activityContext.getParam(ShelfActivityConstants.Params.entityId), activityContext.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStore)));
    }

    private Map<String, String> buildQueryRequestAttr(ActivityContext ctx) {
        Map<String, String> attrMap = Maps.newHashMap();
        attrMap.put("unionId", ctx.getParam(ShelfActivityConstants.Params.unionId));
        //经纬度
        if (ctx.getParam(ShelfActivityConstants.Params.lat) != null && ctx.getParam(ShelfActivityConstants.Params.lng) != null) {
            attrMap.put("lat", ctx.getParam(ShelfActivityConstants.Params.lat).toString());
            attrMap.put("lng", ctx.getParam(ShelfActivityConstants.Params.lng).toString());
        }
        addZdcTagMatchParam(ctx, attrMap);
        // 春节不打烊
        addSpringFestivalNoClose(ctx, attrMap);
        // 透传搜索页品牌信息
        addBrandInfoParam(ctx, attrMap);
        //透传货架堆头类型
        addShelfTopDisplayParam(ctx, attrMap);
        //神券筛选标签
        BPShelfRequestBuilder.addMagicalMemberParam(ctx, attrMap);
        //向bp和选单透传定位城市
        BPShelfRequestBuilder.addLocationCityId(ctx, attrMap);
        //界面来源
        attrMap.put("pageSource", ctx.getParam(ShelfActivityConstants.Params.pageSource));
        //搜索词
        String keyword = ctx.getParam(ShelfActivityConstants.Params.keyword);
        attrMap.put("searchWord",keyword);
        //是否预订货架
        addBookShelfParam(ctx, attrMap);
        //游戏厅游戏币货架仅召回游戏币筛选项
        BPShelfRequestBuilder.addGameCoinParam(ctx, attrMap);
        return attrMap;
    }

    private void addBrandInfoParam(ActivityContext ctx, Map<String, String> attrMap) {
        String summaryProductIds = ctx.getParam(ShelfActivityConstants.Params.summaryProductIds);
        List<String> brandSelect = ParamUtil.extractBySummarypids(summaryProductIds, "brandSelect");
        if (CollectionUtils.isNotEmpty(brandSelect)) {
            attrMap.put(ShelfRequestAttrMapKeys.SEARCH_IMPLANT_BRAND.getKey(), brandSelect.get(0));
        }
    }

    private void addShelfTopDisplayParam(ActivityContext ctx, Map<String, String> attrMap) {
        Map<String, Object> recallConfig = ctx.getParam(QueryFetcher.Params.recallConfig);
        if (MapUtils.isEmpty(recallConfig)) {
            return;
        }

        boolean duiTou = (boolean) ParamsUtil.getValue(recallConfig, QueryFetcher.Params.eduDuiTou, false);
        attrMap.put("eduDuiTou", String.valueOf(duiTou));

        List<DouHuM> douHuMList = ctx.getParam(ShelfActivityConstants.Params.douHus);
        Map<String, String> douHuSk2topDisplayType = (Map<String, String>) ParamsUtil.getValue(recallConfig, QueryFetcher.Params.douHuSk2topDisplayType, new HashMap<>());
        if (MapUtils.isEmpty(douHuSk2topDisplayType)) {
            return;
        }
        Optional<String> topDisplayType = (Optional<String>) DouHuUtils.getConfigByDouHu(douHuMList, douHuSk2topDisplayType);
        if (topDisplayType.isPresent() && StringUtils.isNotEmpty(topDisplayType.get())) {
            attrMap.put("duitouType", topDisplayType.get());
        }
    }

    private void addZdcTagMatchParam(ActivityContext ctx, Map<String, String> attrMap) {
        if(Objects.nonNull(ctx.getParam(ShelfActivityConstants.Params.matchZdcTag))
            && (boolean)ctx.getParam(ShelfActivityConstants.Params.matchZdcTag)){
            attrMap.put("PoiZdcTagMatch", "true");
        }
    }

    public void addSpringFestivalNoClose(ActivityContext ctx, Map<String, String> attrMap){
        if (Objects.nonNull(ctx.getParam(ShelfActivityConstants.Params.springFestivalNoClose))
                && (boolean) ctx.getParam(ShelfActivityConstants.Params.springFestivalNoClose)) {
            attrMap.put("isHolidayClosedShop", "true");
        }
    }

    private Long getFlagshipStoreIdByStoreUUID(String storeUUID, FlagshipStoreM flagshipStoreM) {
        if (flagshipStoreM != null && flagshipStoreM.getStoreId() > 0) {
            return flagshipStoreM.getStoreId();
        }
        if (StringUtils.isEmpty(storeUUID)) {
            return 0L;
        }
        try {
            return atomFacadeService.getStoreIdByUUID(storeUUID).get(200, TimeUnit.MILLISECONDS);
        } catch (Exception ex) {
            Cat.logError(ex);
            return 0L;
        }
    }

    private CompletableFuture<ProductShelf> getShelfFuture(ShelfRequest navRequest, String sceneCode, Map<String, String> filterComponent2Group) {
        if (!validateFilterParam(navRequest)) {
            return CompletableFuture.completedFuture(null);
        }
        long shopId = Platform.MT.getSource() == navRequest.getPlatform() ? navRequest.getMtPoiid() : navRequest.getDpPoiid();
        //开关、灰度策略
        if (!useFilterCache(shopId, sceneCode)) {
            return atomFacadeService.multiGetShelfNav(navRequest).thenApply(result->convertToFilterShelf(navRequest.getUserId(),result, sceneCode, filterComponent2Group));
        }
        CacheKey cacheKey = buildFilterCacheKey(navRequest.getPlatform(), shopId);
        DataLoader<ProductShelf> dataLoader = key -> {
            if (key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return atomFacadeService.multiGetShelfNav(navRequest).thenApply(result->convertToFilterShelf(navRequest.getUserId(),result, sceneCode, filterComponent2Group));
        };
        CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);
        return cacheClient.asyncGetReadThrough(cacheKey, filterCacheTypeReference, dataLoader, platformFilterCacheExpireTime + new Random().nextInt(300), platformFilterCacheRefreshTime);
    }

    private ProductShelf convertToFilterShelf(Long userId,Response<ShelfDTO> response, String sceneCode, Map<String, String> filterComponent2Group) {
        LogUtils.recordKeyMsg(userId,"multiGetShelfNavEnd",response);
        if (response == null || !response.isSuccess()) {
            return null;
        }
        List<ShelfComponent> shelfComponentList = Optional
                .of(response)
                .map(Response::getContent)
                .map(ShelfDTO::getShelfComponentList)
                .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(shelfComponentList)) {
            return null;
        }
        ProductShelf productShelf = new ProductShelf();
        productShelf.setShelfComponents(shelfComponentList.stream().filter(Objects::nonNull).map(s -> convertToFilterGroup(s, sceneCode, filterComponent2Group)).collect(Collectors.toList()));
        return productShelf;
    }

    private void logSceneMapping(String stlScene, String bpScene, boolean hasResult) {
        // 记录场景映射的打点信息
        Map<String, String> tagMap1 = Maps.newHashMap();
        tagMap1.put("stl2bp", String.format("%s#%s", stlScene, bpScene));
        tagMap1.put("hasResult", Boolean.toString(hasResult));
        Cat.logMetricForCount("SceneMapping", 1, tagMap1);

        // 记录bp场景打点信息
        Map<String, String> tagMap2 = Maps.newHashMap();
        tagMap2.put("bpScene", bpScene);
        tagMap2.put("hasResult", Boolean.toString(hasResult));
        Cat.logMetricForCount("BpScene", 1, tagMap2);
    }

    private FilterGroup convertToFilterGroup(ShelfComponent shelfComponent, String sceneCode, Map<String, String> filterComponent2Group) {
        FilterGroup filterGroup = new FilterGroup();
        filterGroup.setGroupId(shelfComponent.getId());
        filterGroup.setScene(shelfComponent.getScene());
        filterGroup.setName(shelfComponent.getName());
        if (shelfComponent.getShelfNavComponent() != null && CollectionUtils.isNotEmpty(shelfComponent.getShelfNavComponent().getShelfNavTagList())) {
            filterGroup.setFilters(shelfComponent.getShelfNavComponent().getShelfNavTagList().stream().map(this::convertToShelfFilter).collect(Collectors.toList()));
        }
        // 对被使用的BP场景加打点
        if (filterComponent2Group.containsKey(shelfComponent.getName())) {
            logSceneMapping(sceneCode, shelfComponent.getScene(), shelfComponent.getShelfNavComponent() != null && CollectionUtils.isNotEmpty(shelfComponent.getShelfNavComponent().getShelfNavTagList()));
        }
        filterGroup.setShelfTags(getShelfTags(shelfComponent));
        filterGroup.setProductTotalCount(shelfComponent.getProductTotalSize());
        return filterGroup;
    }

    private List<String> getShelfTags(ShelfComponent shelfComponent) {
        if (CollectionUtils.isEmpty(shelfComponent.getShelfComponentTagList())) {
            return null;
        }
        return shelfComponent.getShelfComponentTagList()
                .stream()
                .filter(shelfComponentTag -> StringUtils.isNotEmpty(shelfComponentTag.getDesc()))
                .map(ShelfComponentTag::getDesc)
                .collect(Collectors.toList());
    }

    private ShelfFilter convertToShelfFilter(NavTag navTag) {
        ShelfFilter filter = new ShelfFilter();
        filter.setFilterId(navTag.getId());
        filter.setFilterName(navTag.getName());
        filter.setTotalCount(navTag.getTotalCount());
        filter.setNavTagType(navTag.getNavTagType());
        if (CollectionUtils.isNotEmpty(navTag.getNavTagOptionList())) {
            ShelfFilterOption shelfFilterOption = new ShelfFilterOption();
            shelfFilterOption.setChildren(navTag.getNavTagOptionList().stream()
                    .map(this::convertToFilterOption)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            filter.setFilterOptionTree(shelfFilterOption);
        }
        if (CollectionUtils.isNotEmpty(navTag.getChildNavTag())) {
            filter.setChildren(navTag.getChildNavTag()
                    .stream()
                    .map(this::convertToShelfFilter)
                    .collect(Collectors.toList()));
        }
        filter.setActivity(ExtendDataDtoGsonUtil.isActivityNavTag(navTag));
        filter.setActivityStyle(convertToActivityStyle(navTag));
        return filter;
    }

    private ActivityStyle convertToActivityStyle(NavTag navTag){
        ActivityNavStyleDTO activityNavStyleDTO = ExtendDataDtoGsonUtil.getActivityNavStyle(navTag);
        if(activityNavStyleDTO == null){
            return null;
        }
        ActivityStyle activityStyle = new ActivityStyle();
        activityStyle.setIconUrl(activityNavStyleDTO.getIconUrl());
        activityStyle.setHeight(NumberUtils.toInt(activityNavStyleDTO.getIconHeight()));
        activityStyle.setWidth(NumberUtils.toInt(activityNavStyleDTO.getIconWidth()));
        return activityStyle;
    }

    private ShelfFilterOption convertToFilterOption(NavTagOption navTagOption) {
        if(navTagOption.getOptionIdentity() == null){
            return null;
        }
        ShelfFilterOption filterOption = new ShelfFilterOption();
        filterOption.setIdentityName(navTagOption.getOptionIdentity().getIdentityName());
        filterOption.setDisplayName(navTagOption.getOptionIdentity().getDisplayName());
        filterOption.setMultipleSelect(navTagOption.isMultipleSelect());
        if (CollectionUtils.isNotEmpty(navTagOption.getChildOptionList())) {
            filterOption.setChildren(navTagOption.getChildOptionList().stream()
                    .map(this::convertToFilterOption)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        return filterOption;
    }

    private boolean validateFilterParam(ShelfRequest navRequest) {
        if (navRequest == null) {
            return false;
        }
        if (Platform.of(navRequest.getPlatform()) == null) {
            return false;
        }
        if (navRequest.getDpPoiid() <= 0 && navRequest.getMtPoiid() <= 0 && navRequest.getShelfType() != ShelfTypeEnum.FLAGSHIP_STORE_SHELF) {
            return false;
        }
        return true;
    }

    private boolean useFilterCache(long shopId, String sceneCode) {
        ShelfSceneCacheConfig sceneCacheConfig = ShelfSceneCacheConfig.getCacheConfigBySceneCode(sceneCode);
        if (sceneCacheConfig == null) {
            return false;
        }
        return filterCacheSwitch && isGrey(shopId) && sceneCacheConfig.isUseFilterCache();
    }

    private CacheKey buildFilterCacheKey(int platform, long shopId) {
        return new CacheKey(REDIS_FILTER_CATEGORY, platform, shopId);
    }

    /**
     * 灰度策略
     *
     * @param shopId 当次查询门店ID
     * @return
     */
    private boolean isGrey(long shopId) {
        if (filterGreyConfig == null) {
            return true;
        }
        return filterGreyConfig.isGrey(shopId);
    }

    private void addBookShelfParam(ActivityContext ctx, Map<String, String> attrMap) {
        Map<String, Object> recallConfig = ctx.getParam(QueryFetcher.Params.recallConfig);
        if (MapUtils.isEmpty(recallConfig)) {
            return;
        }
        String isBook = (String) ParamsUtil.getValue(recallConfig, QueryFetcher.Params.isBook, null);
        attrMap.put("isBook", isBook);
    }

    @Data
    private static class ProductShelf implements Serializable {

        /**
         * 货架列表
         */
        private List<FilterGroup> shelfComponents;
    }

    @Data
    private static class FilterGroup implements Serializable {

        /**
         * 货架标识
         */
        private long groupId;

        /**
         * 货架名称
         */
        private String name;

        /**
         * 货架场景
         */
        private String scene;

        /**
         * 筛选列表
         */
        private List<ShelfFilter> filters;

        /**
         * 货架随时退等标签
         */
        private List<String> shelfTags;

        /**
         * 货架商品总数
         */
        private int productTotalCount;
    }

    @Data
    private static class ShelfFilter implements Serializable {

        /**
         * 筛选ID
         */
        private long filterId;

        /**
         * 筛选名称
         */
        private String filterName;

        /**
         * 筛选下商品数量
         */
        private int totalCount;

        /***
         * 是否活动筛选
         */
        private boolean isActivity;

        /**
         * 活动筛选样式
         */
        private ActivityStyle activityStyle;

        /**
         * 筛选选项树
         */
        private ShelfFilterOption filterOptionTree;

        /**
         * 子筛选
         */
        private List<ShelfFilter> children;

        /**
         * 导航标签类型，参考枚举:{@link com.dianping.product.shelf.common.enums.NavTagTypeEnum}
         */
        private Integer navTagType;
    }

    @Data
    private static class ShelfFilterOption implements Serializable {

        /**
         * 筛选选项标志名
         */
        private String identityName;

        /**
         * 筛选选项展示名
         */
        private String displayName;

        /**
         * 下一级子选项是否多选
         */
        private boolean multipleSelect;

        /**
         * 子选项
         */
        private List<ShelfFilterOption> children;
    }

    private Map<String, FilterM> postProcessFilters(ActivityContext ctx, Map<String, FilterM> filterMs) {
        // 1. Filter拦截
        interceptFilter(ctx, filterMs);
        // 2. FilterBtn拦截
        interceptFilterBtn(ctx, filterMs);
        return filterMs;
    }

    private void interceptFilter(ActivityContext activityContext, Map<String, FilterM> filterMs) {
        if (MapUtils.isEmpty(filterMs)) {
            return;
        }

        filterMs.forEach((groupName, filterM) -> endIntercept(activityContext, groupName, filterM));
    }

    private void interceptFilterBtn(ActivityContext activityContext, Map<String, FilterM> filterMs) {
        if (MapUtils.isEmpty(filterMs)) {
            return;
        }
        filterMs.forEach((groupName, filterM) -> {
            if (org.apache.commons.collections.CollectionUtils.isEmpty(filterM.getFilters())) return;
            filterM.getFilters().forEach(filterBtnM -> endIntercept(activityContext, groupName, filterBtnM));
        });
    }


    private void endIntercept(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        findExtPoint(activityContext, FilterFetcherExt.EXT_POINT_FILTER_CODE).endIntercept(activityContext, groupName, filterBtnM);
    }

    private void endIntercept(ActivityContext activityContext, String groupName, FilterM filterM) {
        findExtPoint(activityContext, FilterFetcherExt.EXT_POINT_FILTER_CODE).endIntercept(activityContext, groupName, filterM);
    }


}
