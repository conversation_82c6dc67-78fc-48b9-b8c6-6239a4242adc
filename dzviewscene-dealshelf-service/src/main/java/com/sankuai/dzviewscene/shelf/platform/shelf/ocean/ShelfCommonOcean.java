package com.sankuai.dzviewscene.shelf.platform.shelf.ocean;

import cn.hutool.core.map.MapUtil;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils.UnifiedShelfCommonOcean;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.RegExCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.enums.OceanTypeEnums;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.utils.OceanConfigUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.utils.OceanConstantUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sig.botdefender.core.crypt.utils.SigCryptUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils.MAGICAL_MEMBER_TAG_NAME;

/**
 * 货架打点工具包
 * Created by zhangsuping on 2022/4/2.
 */
public class ShelfCommonOcean {

    public static final String PRICE_PREFIX = "神券价";

    /**
     * 填充公共打点信息
     *
     * @param shelfOceanVO
     * @param shelfComponent
     * @param ctx
     * @return
     */
    public static ShelfOceanVO paddingCommonOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        try {
            if (OceanConfigUtils.switchOff() || shelfComponent == null) {
                return shelfOceanVO;
            }
            shelfOceanVO = initialShelfOceanIfNull(shelfOceanVO);
            //填充整个货架Labs的打点
            paddingWholeLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第一个商品区更多Labs的打点
            paddingMoreLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第二个商品区更多Labs的打点
            paddingSecondMoreLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第一个商品区Item的Labs的打点
            paddingProductItemLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第二个商品区Item的Labs的打点
            paddingSecondProductItemLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第一层筛选项的Labs的打点
            paddingFilterBarLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第二层筛选项的Labs的打点
            paddingChildrenFilterBarLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充商品区商品标签打点
            paddingProductItemTagLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充商品区商品底部标签打点
            paddingProductBottomTagLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充筛选弹窗的Labs的打点
            paddingFilterPickerLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充筛选项个性化Labs的公共打点
            paddingFilterLabsOcean(shelfComponent, ctx);
            //填充商品域的公共打点
            paddingProductAreaOcean(shelfComponent, ctx);
            return shelfOceanVO;
        } catch (Exception e) {
            Cat.logError(e);
        }
        return shelfOceanVO;
    }

    private static void paddingFilterPickerLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfComponent.getFilterTree() == null || CollectionUtils.isEmpty(shelfComponent.getFilterTree().getChildren())) {
            return;
        }
        if (shelfOceanVO.getFilterPicker() == null) {
            shelfOceanVO.setFilterPicker(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponent = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO filterPickerByConfig = getFilterPickerByConfig(ctx);
        shelfOceanVO.setFilterPicker(getAndSetOceanLabsEntry(shelfOceanVO.getFilterPicker(), productAreaComponent, shelfComponent, ctx, filterPickerByConfig, null));
    }

    private static DzShelfOceanEntryVO getFilterPickerByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getFilterPicker();
    }

    private static void paddingChildrenFilterBarLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if ((shelfComponent.getFilter() == null || CollectionUtils.isEmpty(shelfComponent.getFilter().getFilterBtns()))
            && (shelfComponent.getFilterTree() == null || CollectionUtils.isEmpty(shelfComponent.getFilterTree().getChildren()))) {
            return;
        }
        if (shelfOceanVO.getChildrenFilterBar() == null) {
            shelfOceanVO.setChildrenFilterBar(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponent = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO childrenFilterBarConfig = getChildrenFilterBarByConfig(ctx);
        //需要塞默认值
        Map<String, Object> oceanEntryMap = new HashMap<>();
        oceanEntryMap.put(OceanConstantUtils.ITEM_TYPE, 0);
        oceanEntryMap.put(OceanConstantUtils.SHOW_MODE, "-999");
        shelfOceanVO.setChildrenFilterBar(getAndSetOceanLabsEntry(shelfOceanVO.getChildrenFilterBar(), productAreaComponent, shelfComponent, ctx, childrenFilterBarConfig, oceanEntryMap));
    }

    private static DzShelfOceanEntryVO getChildrenFilterBarByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getChildrenFilterBar();
    }

    private static void paddingFilterBarLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if ((shelfComponent.getFilter() == null || CollectionUtils.isEmpty(shelfComponent.getFilter().getFilterBtns()))
             && (shelfComponent.getFilterTree() == null || CollectionUtils.isEmpty(shelfComponent.getFilterTree().getChildren()))) {
            return;
        }
        if (shelfOceanVO.getFilterBar() == null) {
            shelfOceanVO.setFilterBar(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponent = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO filterBarConfig = getFilterBarByConfig(ctx);
        //需要塞默认值
        Map<String, Object> oceanEntryMap = new HashMap<>();
        oceanEntryMap.put(OceanConstantUtils.ITEM_TYPE, 0);
        oceanEntryMap.put(OceanConstantUtils.SHOW_MODE, "-999");
        shelfOceanVO.setFilterBar(getAndSetOceanLabsEntry(shelfOceanVO.getFilterBar(), productAreaComponent, shelfComponent, ctx, filterBarConfig, oceanEntryMap));
    }

    private static DzShelfOceanEntryVO getFilterBarByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getFilterBar();
    }

    private static ShelfOceanVO initialShelfOceanIfNull(ShelfOceanVO shelfOceanVO) {
        if (shelfOceanVO == null) {
            return new ShelfOceanVO();
        }
        return shelfOceanVO;
    }

    private static void paddingProductAreaOcean(DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas())) {
            return;
        }
        List<Long> filterBtnIdsSelected = getFilterBtnIdsSelected(shelfComponent.getFilter());
        boolean hasMultiFilterIdAndProductAreas = shelfComponent.getFilterIdAndProductAreas().size() > 1;
        shelfComponent.getFilterIdAndProductAreas().forEach(filterIdAndProductArea -> {
            if (filterIdAndProductArea == null || CollectionUtils.isEmpty(filterIdAndProductArea.getProductAreas())) {
                return;
            }
            filterIdAndProductArea.getProductAreas().forEach(productArea -> {
                if (productArea == null) {
                    return;
                }
                paddingItemAreaComponentLabs(shelfComponent, productArea, ctx, filterIdAndProductArea.getFilterBtnId(), filterBtnIdsSelected, hasMultiFilterIdAndProductAreas);
            });
        });
    }

    private static void paddingProductItemLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getProductItem() == null) {
            shelfOceanVO.setProductItem(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponent = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO productItemConfig = getProductItemOceanByConfig(ctx);
        shelfOceanVO.setProductItem(getAndSetOceanLabsEntry(shelfOceanVO.getProductItem(), productAreaComponent, shelfComponent, ctx, productItemConfig, null));
    }

    private static void paddingProductItemTagLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getProductItemTag() == null) {
            shelfOceanVO.setProductItemTag(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponent = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO productItemTagConfig = getProductItemTagOceanByConfig(ctx);
        shelfOceanVO.setProductItemTag(getAndSetOceanLabsEntry(shelfOceanVO.getProductItemTag(), productAreaComponent, shelfComponent, ctx, productItemTagConfig, null));
    }

    private static void paddingProductBottomTagLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getProductBottomTag() == null) {
            shelfOceanVO.setProductBottomTag(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponent = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO productBottomTagConfig = getProductBottomTagOceanByConfig(ctx);
        shelfOceanVO.setProductBottomTag(getAndSetOceanLabsEntry(shelfOceanVO.getProductBottomTag(), productAreaComponent, shelfComponent, ctx, productBottomTagConfig, null));
    }

    private static void paddingSecondProductItemLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getSecondProductItem() == null) {
            shelfOceanVO.setSecondProductItem(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponentVO = getSecondProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO secondProductItemConfig = getSecondProductItemOceanByConfig(ctx);
        shelfOceanVO.setSecondProductItem(getAndSetOceanLabsEntry(shelfOceanVO.getSecondProductItem(), productAreaComponentVO, shelfComponent, ctx, secondProductItemConfig, null));
    }

    private static DzShelfOceanEntryVO getSecondProductItemOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getSecondProductItem();
    }

    private static DzShelfOceanEntryVO getProductItemOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getProductItem();
    }

    private static DzShelfOceanEntryVO getProductItemTagOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getProductItemTag();
    }

    private static DzShelfOceanEntryVO getProductBottomTagOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getProductBottomTag();
    }

    private static void paddingMoreLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        //一级更多如果为空，则进行初始化
        if (shelfOceanVO.getMore() == null) {
            shelfOceanVO.setMore(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponentVO = getFirstProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO moreOceanConfig = getMoreOceanByConfig(ctx);
        shelfOceanVO.setMore(getAndSetOceanLabsEntry(shelfOceanVO.getMore(), productAreaComponentVO, shelfComponent, ctx, moreOceanConfig, null));
    }

    private static DzShelfOceanEntryVO getMoreOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getMore();
    }

    private static ProductAreaComponentVO getSecondProductAreaComponentVO(DzShelfComponentVO shelfComponent) {
        if (CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas())
                || shelfComponent.getFilterIdAndProductAreas().get(0) == null
                || CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas())
                || shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().size() <= 1
                || shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(1) == null) {
            return new ProductAreaComponentVO();
        }
        return shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(1);
    }

    private static void paddingSecondMoreLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getSecondMore() == null) {
            shelfOceanVO.setSecondMore(new DzShelfOceanEntryVO());
        }
        ProductAreaComponentVO productAreaComponentVO = getSecondProductAreaComponentVO(shelfComponent);
        DzShelfOceanEntryVO secondMoreOceanConfig = getSecondMoreOceanByConfig(ctx);
        shelfOceanVO.setSecondMore(getAndSetOceanLabsEntry(shelfOceanVO.getSecondMore(), productAreaComponentVO, shelfComponent, ctx, secondMoreOceanConfig, null));
    }

    private static DzShelfOceanEntryVO getSecondMoreOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getSecondMore();
    }

    private static String getModuleNameByTitleAndMainTitleComponent(String sceneCode, TitleComponentVO titleComponent, MainTitleComponentVO mainTitleComponent, ActivityContext ctx) {
        //首先返回场景自定义的标题
        String mainTitle = OceanConfigUtils.getMainTitleByConfig(sceneCode, ctx);
        if (StringUtils.isNotEmpty(mainTitle)) {
            return getTitleByFilterSpecialCharacter(mainTitle);
        }
        //货架主标题组件中的标题
        String title = mainTitleComponent == null ? null : mainTitleComponent.getTitle();
        //优先展示商品区标题
        if (titleComponent != null && StringUtils.isNotEmpty(titleComponent.getTitle())) {
            title = titleComponent.getTitle();
        }
        return getTitleByFilterSpecialCharacter(title);
    }

    private static String getModuleName(DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        //首先返回场景自定义的标题
        String mainTitle = OceanConfigUtils.getMainTitleByConfig(shelfComponent.getSceneCode(), ctx);
        if (StringUtils.isNotEmpty(mainTitle)) {
            return getTitleByFilterSpecialCharacter(mainTitle);
        }
        if (shelfComponent.getMainTitle() == null || StringUtils.isEmpty(shelfComponent.getMainTitle().getTitle())) {
            //兜底第一个商品区的货架标题
            return getTitleByFilterSpecialCharacter(getFirstProductAreaTitle(shelfComponent));
        }
        //货架主标题名称
        return getTitleByFilterSpecialCharacter(shelfComponent.getMainTitle().getTitle());
    }

    private static String getTitleByFilterSpecialCharacter(String title) {
        if (StringUtils.isEmpty(title)) {
            return null;
        }
        RegExCfgBean regExCfgBean = OceanConfigUtils.getRegExConstant();
        if (regExCfgBean == null || StringUtils.isEmpty(regExCfgBean.getTitleRegEx())) {
            return title;
        }
        return Pattern.compile(regExCfgBean.getTitleRegEx()).matcher(title).replaceAll("").trim();
    }

    private static void paddingItemAreaComponentLabs(DzShelfComponentVO shelfComponent, ProductAreaComponentVO productAreaComponentVO, ActivityContext ctx, long filterBtnId,  List<Long> filterBtnIdsSelected, boolean hasMultiFilterIdAndProductAreas) {
        if (productAreaComponentVO.getItemArea() == null || CollectionUtils.isEmpty(productAreaComponentVO.getItemArea().getProductItems())) {
            return;
        }
        Map<Integer, ProductM> productMMap = getProductMMap(ctx);
        String flowId = getRecommendFlowId(ctx);
        AtomicInteger index = new AtomicInteger(0);
        productAreaComponentVO.getItemArea().getProductItems().forEach(productItem -> {
            if (productItem == null) {
                return;
            }
            int currentIndex = index.getAndIncrement();
            String productType = getType(shelfComponent.getSceneCode(), ctx);
            Map<String, Object> labsOceanMap = getLabsOceanMapByLab(productItem.getLabs());
            labsOceanMap.put(OceanConstantUtils.STATUS, getStatus(productAreaComponentVO.getItemArea().getDefaultShowNum(), currentIndex, filterBtnId, filterBtnIdsSelected, ctx, hasMultiFilterIdAndProductAreas));
            labsOceanMap.put(OceanConstantUtils.INDEX, currentIndex);
            labsOceanMap.put(OceanConstantUtils.PRICE, getPrice(productItem));
            labsOceanMap.put(OceanConstantUtils.DZ_REAL_QUERY_ID, flowId);
            labsOceanMap.put(OceanConstantUtils.PRICE_TITLE, getPriceTitle(productItem));
            // 先用后付标签副标题上报
            labsOceanMap.put(OceanConstantUtils.LABEL_NAME, getLabelName(labsOceanMap, productItem));
            //到综搜推核心展位实时特征打点
            labsOceanMap.put(OceanConstantUtils.DZ_ABTEST, getRecRealTimeInfos(productMMap,productItem.getItemId()));
            //图片URL的上报
            labsOceanMap.put(OceanConstantUtils.PIC_URL, getPicUrl(productItem));
            //神会员打点上报
            paddingMagicalMemberLabs(labsOceanMap, productItem);
            //团购的商品ID，用deal_id承接商品打点
            if (StringUtils.isNotEmpty(productType) && productType.contains(OceanTypeEnums.DEAL.getDesc())) {
                labsOceanMap.put(OceanConstantUtils.DEAL_ID, productItem.getItemIdL());
                productItem.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
                return;
            }
            //除了团购的其他商品用product_id承接商品打点
            labsOceanMap.put(OceanConstantUtils.PRODUCT_ID, productItem.getItemIdL());
            productItem.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
        });
    }

    private static Object getPriceTitle(DzItemVO itemVO) {
        String pricePrefix = itemVO.getSalePricePrefixDesc();
        if (StringUtils.isNotEmpty(pricePrefix) && PRICE_PREFIX.equals(pricePrefix)) {
            return pricePrefix;
        }
        return StringUtils.EMPTY;
    }

    private static String getLabelName(Map<String, Object> labsOceanMap,DzItemVO productItem) {
        Object labelNameObj = labsOceanMap.get(OceanConstantUtils.LABEL_NAME);
        String labelName = labelNameObj != null ? String.valueOf(labelNameObj) : "";
        if (productItem == null || CollectionUtils.isEmpty(productItem.getProductRichTags())) {
            return labelName;
        }
        if (CollectionUtils.isNotEmpty(productItem.getProductRichTags())) {
            boolean hasPayLaterTag = productItem.getProductRichTags().stream().filter(Objects::nonNull).anyMatch(tag -> tag.getText().contains(UnifiedShelfCommonOcean.payLaterTag));
            if (hasPayLaterTag) {
                return StringUtils.isNotEmpty(labelName) && !"-999".equals(labelName) ? labelName + "," + UnifiedShelfCommonOcean.payLaterTag : UnifiedShelfCommonOcean.payLaterTag;
            }
        }
        return labelName;
    }

    public static List<Map<String, String>> getRecRealTimeInfos(Map<Integer, ProductM> productMMap, int itemId) {
        if (productMMap == null) {
            return Collections.emptyList();
        }
        ProductM productM = productMMap.get(itemId);
        if (productM == null) {
            return Collections.emptyList();
        }
        String recRealTimeInfos = productM.getAttr("recRealTimeInfos");
        if (StringUtils.isEmpty(recRealTimeInfos)) {
            return Collections.emptyList();
        }
        return Arrays.stream(recRealTimeInfos.split(","))
                .map(info -> info.split("#"))
                .filter(split -> split.length >= 2)
                .map(split -> {
                    Map<String, String> infoMap = new HashMap<>();
                    infoMap.put("dz_biz_id", split[0]);
                    infoMap.put("dz_query_id", split[1]);
                    return infoMap;
                })
                .collect(Collectors.toList());
    }

    public static Map<Integer, ProductM> getProductMMap(ActivityContext ctx) {
        CompletableFuture<ShelfGroupM> shelfGroupMCompletableFuture = ctx.getMainData();
        Map<String, ProductGroupM> productGroupMap = Optional.ofNullable(shelfGroupMCompletableFuture.join()).map(ShelfGroupM::getProductGroupMs).orElse(null);
        if(Objects.isNull(productGroupMap)){
            return null;
        }
        int productType = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfType);
        ProductGroupM productGroupM = productGroupMap.get(getGroupName(productType));
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return null;
        }
        return productGroupM.getProducts().stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductM::getProductId, Function.identity(),(o1, o2) -> o1));
    }

    protected static String getGroupName(int productType) {
        return ShelfTypeEnums.getByType(productType).getDesc();
    }

    private static String getPicUrl(DzItemVO productItem) {
        if(Objects.isNull(productItem.getPic()) || Objects.isNull(productItem.getPic().getPic())){
            return StringUtils.EMPTY;
        }
        return productItem.getPic().getPic().getPicUrl();
    }

    private static void paddingMagicalMemberLabs(Map<String, Object> labsOceanMap, DzItemVO productItem) {
        if (CollectionUtils.isEmpty(productItem.getPriceBottomTags())) {
            return;
        }
        DzTagVO dzTagVO = productItem.getPriceBottomTags()
                .stream()
                .filter(Objects::nonNull)
                .filter(tag -> MAGICAL_MEMBER_TAG_NAME.equals(tag.getName())).findFirst()
                .orElse(null);
        if (dzTagVO != null) {
            labsOceanMap.put("god_label", 1);
            labsOceanMap.put("promotion_title", getMagicalTag(dzTagVO));
        }
    }
    
    private static String getMagicalTag(DzTagVO dzTagVO){
        if(dzTagVO.getMagicalTagStyle() != null){
            MagicalTagStyleVO magicalTagStyleVO = dzTagVO.getMagicalTagStyle();
            String preText = magicalTagStyleVO.getRichPreText() != null ? magicalTagStyleVO.getRichPreText().getText() : "";
            String text = CollectionUtils.isNotEmpty(magicalTagStyleVO.getMultiText()) ? String.join("|", magicalTagStyleVO.getMultiText()) : "";
            return "神券" + preText + text;
        }
        return "神券" + dzTagVO.getText();
    }

    private static String getRecommendFlowId(ActivityContext activityContext) {
        if (activityContext == null || MapUtils.isEmpty(activityContext.getParameters())) {
            return null;
        }
        CompletableFuture<String> future = activityContext.getParam(ShelfActivityConstants.Ctx.ctxRecommendFlowId);
        if (future == null) {
            return null;
        }
        try {
            //最多允许影响业务10ms，并行处理
            return future.get(50, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityContext, e);
            return null;
        }
    }

    private static List<Long> getFilterBtnIdsSelected(FilterComponentVO filter) {
        if (filter == null || CollectionUtils.isEmpty(filter.getFilterBtns())) {
            return Lists.newArrayList();
        }
        List<Long> filterBtnIdsSelected = new ArrayList<>();
        filter.getFilterBtns().forEach(filterBtn -> {
            //一级筛选项未选中直接返回
            if (!filterBtn.isSelected()) {
                return;
            }
            filterBtnIdsSelected.add(filterBtn.getFilterBtnId());
            //添加孩子节点选中的筛选ID列表
            setChildrenFilterBtnIdsSelected(filterBtn.getChildren(), filterBtnIdsSelected);
        });
        //没有选中，默认选中第一个筛选项
        if (CollectionUtils.isEmpty(filterBtnIdsSelected)) {
            return buildFirstFilterBtnIdsSelected(filter.getFilterBtns().get(0));
        }
        return filterBtnIdsSelected;
    }

    private static List<Long> buildFirstFilterBtnIdsSelected(FilterButtonVO filterButtonVO) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.shelf.ocean.ShelfCommonOcean.buildFirstFilterBtnIdsSelected(com.sankuai.dzviewscene.productshelf.vu.vo.FilterButtonVO)");
        if (filterButtonVO == null) {
            return new ArrayList<>();
        }
        List<Long> filterBtnIdsSelected = new ArrayList<>();
        filterBtnIdsSelected.add(filterButtonVO.getFilterBtnId());
        setChildrenFilterBtnIdsSelected(filterButtonVO.getChildren(), filterBtnIdsSelected);
        return filterBtnIdsSelected;
    }

    private static void setChildrenFilterBtnIdsSelected(List<FilterButtonVO> children, List<Long> filterBtnIdsSelected) {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        AtomicBoolean childrenFilterSelected = new AtomicBoolean(false);
        children.forEach(filterBtn -> {
            //一级筛选项未选中直接返回
            if (!filterBtn.isSelected()) {
                return;
            }
            childrenFilterSelected.set(true);
            filterBtnIdsSelected.add(filterBtn.getFilterBtnId());
            //添加孩子节点选中的筛选ID列表
            setChildrenFilterBtnIdsSelected(filterBtn.getChildren(), filterBtnIdsSelected);
        });
        //孩子节点选中或第一个孩子节点无效返回
        if (childrenFilterSelected.get() || children.get(0) == null) {
            return;
        }
        //孩子节点未选中，默认选中第一个孩子节点
        filterBtnIdsSelected.add(children.get(0).getFilterBtnId());
    }

    /**
     * 商品是否是默认展示，0-不是、1-是
     *
     * @param defaultShowNum
     * @param index
     * @return
     */
    private static int getStatus(int defaultShowNum, int index, long filterBtnId, List<Long> filterBtnIdsSelected, ActivityContext ctx, boolean hasMultiFilterIdAndProductAreas) {
        //1. 非首屏直接返回0
        if (!shelfFirstLoad(ctx)) {
            return 0;
        }
        //2. filterBtnIdsSelected为空说明没有筛选项，默认列表页展示，此时根据展示个数和当前索引决定状态
        //3. filterBtnIdsSelected不为空说明有筛选项，只有选中的筛选根据展示个数和当前索引决定状态
        if (CollectionUtils.isEmpty(filterBtnIdsSelected) || filterBtnIdsSelected.contains(filterBtnId)) {
            return getStatusByShowNumAndIndex(defaultShowNum, index);
        }
        if (hasMultiFilterIdAndProductAreas) {
            return 0;
        }
        //4. 默认兜底：首屏选中的筛选项与"筛选按钮和商品区关系FilterIdAndProductAreas"中的filterId不匹配的场景
        return getStatusByShowNumAndIndex(defaultShowNum, index);
    }

    private static boolean shelfFirstLoad(ActivityContext ctx) {
        String currentChannel = ctx.getParam(ShelfActivityConstants.Params.channel);
        return ShelfActivityConstants.ChannelType.dealShelfFirstLoad.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.bookingFilter.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.productFilter.equals(currentChannel);
    }

    private static int getStatusByShowNumAndIndex(int defaultShowNum, int index) {
        if (defaultShowNum > index) {
            return 1;
        }
        return 0;
    }

    private static String getPrice(DzItemVO itemVO) {
        String price = itemVO.getSalePriceDesc();
        if (StringUtils.isNotEmpty(itemVO.getSalePrice())) {
            price = itemVO.getSalePrice();
        }
        if (StringUtils.isEmpty(price)) {
            return StringUtils.EMPTY;
        }
        return getPriceByFilterSpecialCharacter(price);
    }

    private static String getPriceByFilterSpecialCharacter(String price) {
        RegExCfgBean regExCfgBean = OceanConfigUtils.getRegExConstant();
        if (regExCfgBean == null || StringUtils.isEmpty(regExCfgBean.getPriceRegEx())) {
            return price;
        }
        return Pattern.compile(regExCfgBean.getPriceRegEx()).matcher(price).replaceAll("").trim();
    }

    private static void paddingFilterLabsOcean(DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        if (shelfComponent.getFilter() == null || CollectionUtils.isEmpty(shelfComponent.getFilter().getFilterBtns())) {
            return;
        }
        AtomicInteger index = new AtomicInteger(0);
        shelfComponent.getFilter().getFilterBtns().forEach(filterBtn -> {
            if (filterBtn == null) {
                return;
            }
            int currentIndex = index.getAndIncrement();
            paddingFilterBasicOcean(filterBtn, ctx, shelfComponent, currentIndex);
            //填充二级筛选项
            paddingSecondFilter(filterBtn.getChildren(), ctx, shelfComponent, filterBtn, currentIndex);
        });
    }

    private static void paddingSecondFilter(List<FilterButtonVO> children, ActivityContext ctx, DzShelfComponentVO shelfComponent, FilterButtonVO parentFilterBtn, int parentFilterIndex) {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        AtomicInteger childFilterIndex = new AtomicInteger(0);
        children.forEach(childFilterBtn -> {
            if (childFilterBtn == null) {
                return;
            }
            int currentIndex = childFilterIndex.getAndIncrement();
            Map<String, Object> labsOceanMap = getLabsOceanMapByLab(childFilterBtn.getLabs());
            labsOceanMap.put(OceanConstantUtils.TITLE, getTitle(childFilterBtn));
            labsOceanMap.put(OceanConstantUtils.INDEX, currentIndex);
            labsOceanMap.put(OceanConstantUtils.TAB_NAME, getTitle(parentFilterBtn));
            labsOceanMap.put(OceanConstantUtils.TAB_INDEX, parentFilterIndex);
            childFilterBtn.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
            //填充三级筛选项
            paddingThirdFilter(childFilterBtn.getChildren(), ctx, shelfComponent);
        });
    }

    private static void paddingThirdFilter(List<FilterButtonVO> filterButtons, ActivityContext ctx, DzShelfComponentVO shelfComponent) {
        if (CollectionUtils.isEmpty(filterButtons)) {
            return;
        }
        AtomicInteger index = new AtomicInteger(0);
        filterButtons.forEach(filterBtn -> {
            if (filterBtn == null) {
                return;
            }
            paddingFilterBasicOcean(filterBtn, ctx, shelfComponent, index.getAndIncrement());
        });
    }

    private static void paddingFilterBasicOcean(FilterButtonVO filterBtn, ActivityContext ctx, DzShelfComponentVO shelfComponent, int index) {
        if (filterBtn == null) {
            return;
        }
        Map<String, Object> labsOceanMap = getLabsOceanMapByLab(filterBtn.getLabs());
        labsOceanMap.put(OceanConstantUtils.TITLE, getTitle(filterBtn));
        labsOceanMap.put(OceanConstantUtils.INDEX, index);
        filterBtn.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
    }

    private static String getTitle(FilterButtonVO filterButton) {
        String title = getFilterText(filterButton.getTitle());
        if (StringUtils.isEmpty(title)) {
            return StringUtils.EMPTY;
        }
        String subTitle = getFilterText(filterButton.getSubTitle());
        if (StringUtils.isEmpty(subTitle)) {
            return title;
        }
        return title + subTitle;
    }

    private static String getFilterText(RichLabelVO richLabelVO) {
        if (richLabelVO == null) {
            return StringUtils.EMPTY;
        }
        return richLabelVO.getText();
    }

    private static void paddingWholeLabsOcean(ShelfOceanVO shelfOceanVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        //整个货架打点如果为空，则进行初始化
        if (shelfOceanVO.getWholeShelf() == null) {
            shelfOceanVO.setWholeShelf(new DzShelfOceanEntryVO());
        }
        //获取个性化labs数据
        Map<String, Object> labsOceanMap = getWholeLabsOceanMap(shelfOceanVO.getWholeShelf(), shelfComponent, ctx);
        shelfOceanVO.getWholeShelf().setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
        ShelfOceanVO shelfOcean = OceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOcean != null && shelfOcean.getWholeShelf() != null) {
            shelfOceanVO.getWholeShelf().setCategory(shelfOcean.getWholeShelf().getCategory());
            shelfOceanVO.getWholeShelf().setBidClick(shelfOcean.getWholeShelf().getBidClick());
            shelfOceanVO.getWholeShelf().setBidView(shelfOcean.getWholeShelf().getBidView());
        }
    }

    private static Map<String, Object> getWholeLabsOceanMap(DzShelfOceanEntryVO shelfOceanEntryVO, DzShelfComponentVO shelfComponent, ActivityContext ctx) {
        //获取个性化labs数据
        Map<String, Object> labsOceanMap = getLabsOceanMap(shelfOceanEntryVO);
        //设置公共打点数据
        labsOceanMap.put(OceanConstantUtils.POI_ID, getPoiId(ctx));
        // poiid加密 有开关
        if (Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE) == null || !"false".equals(Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE))) {

            if (getPoiId(ctx) > 0 && !LionConfigHelper.isSigDegrade()){
                Long poiId = Long.valueOf(getPoiId(ctx));
                labsOceanMap.put(OceanConstantUtils.POI_ID_ENCRYPT, SigCryptUtils.encryptPoiId(poiId));
            }
        }
        labsOceanMap.put(OceanConstantUtils.MODULE_NAME, getModuleName(shelfComponent, ctx));
        labsOceanMap.put(OceanConstantUtils.TYPE, getType(shelfComponent.getSceneCode(), ctx));
        labsOceanMap.put(OceanConstantUtils.CHIMERA_COMMON, buildChimeraCommonLab(shelfOceanEntryVO, shelfComponent.getSceneCode()));
        return labsOceanMap;
    }

    private static String buildChimeraCommonLab(DzShelfOceanEntryVO shelfOceanEntryVO, String sceneCode) {
        Map<String, String> chimeraCommonLabMap = new HashMap<>();
        chimeraCommonLabMap.put(OceanConstantUtils.SCENE_CODE, sceneCode);
        if (StringUtils.isNotEmpty(shelfOceanEntryVO.getBidClick())) {
            chimeraCommonLabMap.put(OceanConstantUtils.OLD_BID_MC, shelfOceanEntryVO.getBidClick());
        }
        if (StringUtils.isNotEmpty(shelfOceanEntryVO.getBidView())) {
            chimeraCommonLabMap.put(OceanConstantUtils.OLD_BID_MV, shelfOceanEntryVO.getBidView());
        }
        return JsonCodec.encode(chimeraCommonLabMap);
    }

    private static DzShelfOceanEntryVO getAndSetOceanLabsEntry(DzShelfOceanEntryVO shelfOceanEntryVO, ProductAreaComponentVO productAreaComponentVO, DzShelfComponentVO shelfComponent,
                                                               ActivityContext ctx, DzShelfOceanEntryVO shelfOceanEntryConfig, Map<String, Object> oceanEntryMap) {
        if (shelfOceanEntryVO == null) {
            shelfOceanEntryVO = new DzShelfOceanEntryVO();
        }
        Map<String, Object> labsOceanMap = getLabsOceanMapByLab(shelfOceanEntryVO.getLabs());
        if (MapUtils.isNotEmpty(oceanEntryMap)) {
            labsOceanMap.putAll(oceanEntryMap);
        }
        labsOceanMap.put(OceanConstantUtils.POI_ID, getPoiId(ctx));
        // poiid加密 有开关
        if (Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE) == null || !"false".equals(Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE))) {
            if (getPoiId(ctx) > 0 && !LionConfigHelper.isSigDegrade()){
                Long poiId = Long.valueOf(getPoiId(ctx));
                labsOceanMap.put(OceanConstantUtils.POI_ID_ENCRYPT, SigCryptUtils.encryptPoiId(poiId));
            }
        }
        labsOceanMap.put(OceanConstantUtils.CITY_ID, getCityId(ctx));
        labsOceanMap.put(OceanConstantUtils.MODULE_NAME, getModuleNameByTitleAndMainTitleComponent(shelfComponent.getSceneCode(), productAreaComponentVO.getTitle(), shelfComponent.getMainTitle(), ctx));
        labsOceanMap.put(OceanConstantUtils.TYPE, getType(shelfComponent.getSceneCode(), ctx));
        labsOceanMap.put(OceanConstantUtils.CHIMERA_COMMON, buildChimeraCommonLab(shelfOceanEntryVO, shelfComponent.getSceneCode()));
        DzShelfOceanEntryVO currentShelfOcean = new DzShelfOceanEntryVO();
        currentShelfOcean.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
        currentShelfOcean.setAbtest(shelfOceanEntryVO.getAbtest());
        if (shelfOceanEntryConfig == null) {
            currentShelfOcean.setBidView(shelfOceanEntryVO.getBidView());
            currentShelfOcean.setBidClick(shelfOceanEntryVO.getBidClick());
            currentShelfOcean.setCategory(shelfOceanEntryVO.getCategory());
            return currentShelfOcean;
        }
        currentShelfOcean.setBidView(shelfOceanEntryConfig.getBidView());
        currentShelfOcean.setBidClick(shelfOceanEntryConfig.getBidClick());
        currentShelfOcean.setCategory(shelfOceanEntryConfig.getCategory());
        return currentShelfOcean;
    }

    private static String getType(String sceneCode, ActivityContext ctx) {
        //首先返回场景自定义的类型
        String type = OceanConfigUtils.getTypeByConfig(sceneCode);
        if (StringUtils.isNotEmpty(type)) {
            return type;
        }
        String currentChannel = ctx.getParam(ShelfActivityConstants.Params.channel);
        //团购
        if (ShelfActivityConstants.ChannelType.dealShelfFirstLoad.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.dealShelfListForTab.equals(currentChannel)) {
            return OceanTypeEnums.DEAL.getDesc();
        }
        //预付
        if (ShelfActivityConstants.ChannelType.productFilter.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.productProducts.equals(currentChannel)) {
            return OceanTypeEnums.PREPAY.getDesc();
        }
        //预定
        if (ShelfActivityConstants.ChannelType.bookingFilter.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.bookingProducts.equals(currentChannel)) {
            return OceanTypeEnums.BOOK.getDesc();
        }
        return null;
    }

    private static String getFirstProductAreaTitle(DzShelfComponentVO shelfComponent) {
        ProductAreaComponentVO productAreaComponentVO = getFirstProductAreaComponentVO(shelfComponent);
        if (productAreaComponentVO.getTitle() == null) {
            return null;
        }
        return productAreaComponentVO.getTitle().getTitle();
    }

    private static ProductAreaComponentVO getFirstProductAreaComponentVO(DzShelfComponentVO shelfComponent) {
        if (CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas())
                || shelfComponent.getFilterIdAndProductAreas().get(0) == null
                || CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas())
                || shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(0) == null) {
            return new ProductAreaComponentVO();
        }
        return shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(0);
    }

    public static long getPoiId(ActivityContext ctx) {
        return ParamsUtil.getLongShopIdForShelfActivity(ctx);
    }

    private static int getCityId(ActivityContext ctx) {
        if (PlatformUtil.isMT(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform))) {
            return ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.mtCityId);
        }
        return ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.dpCityId);
    }

    private static Map<String, Object> getLabsOceanMap(DzShelfOceanEntryVO shelfOceanEntryVO) {
        if (shelfOceanEntryVO == null) {
            return new HashMap<>();
        }
        return getLabsOceanMapByLab(shelfOceanEntryVO.getLabs());
    }

    private static Map<String, Object> getLabsOceanMapByLab(String labs) {
        Map<String, Object> map = JsonCodec.decode(labs, new TypeReference<Map<String, Object>>() {
        });
        if (MapUtil.isEmpty(map)) {
            return new HashMap<>();
        }
        return map;
    }

}