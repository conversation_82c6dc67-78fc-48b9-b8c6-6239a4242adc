package com.sankuai.dztheme.shelf.nr.atom;

import com.dianping.account.dto.ThirdAuthUserInfoDTO;
import com.dianping.account.enums.ThirdAuthTypeEnum;
import com.dianping.apollo.brand.proposal.external.api.dto.BrandDTO;
import com.dianping.aqc.license.dto.ShopLicenseDTO;
import com.dianping.aqc.license.dto.ShopLicenseNewDTO;
import com.dianping.baby.timeLimitTg.dto.BabyTimeLimeTgSpikeDTO;
import com.dianping.bird.product.tohome.dto.phonerepair.PhoneRepairBrandAndModelRequest;
import com.dianping.bird.product.tohome.dto.phonerepair.PhoneRepairProductListDTO;
import com.dianping.carnation.dto.tagtree.HeavyMedicalTagMappingReq;
import com.dianping.carnation.dto.tagtree.HeavyMedicalTagMappingRes;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.shop.dto.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.dppoi.api.dto.DpPoiCategoryDTO;
import com.dianping.dzd.delivery.api.dto.DeliveryConfigAndFeeDTO;
import com.dianping.dzd.delivery.api.request.DeliveryConfigAndFeeRequest;
import com.dianping.dzopen.gateway.api.core.dto.ThirdPartyHttpRequest;
import com.dianping.dztrade.order.business.api.dto.ShopOrderConfigDTO;
import com.dianping.dztrade.order.business.api.request.ShopOrderConfigRequest;
import com.dianping.dztrade.purchase.api.common.dto.ShopPurchaseConfigDTO;
import com.dianping.dztrade.purchase.api.protocol.PurchaseResponse;
import com.dianping.dztrade.purchase.api.request.ShopPurchaseConfigRequest;
import com.dianping.dztrade.refund.api.bean.ShopRefundConfigDTO;
import com.dianping.dztrade.refund.api.request.ShopRefundConfigRequest;
import com.dianping.education.admin.dto.EduVideoDetailDTO;
import com.dianping.education.common.EduResponse;
import com.dianping.education.teacher.api.dto.TeacherDDTO;
import com.dianping.general.unified.search.api.generalsearchv2.dto.DealGroupDto;
import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductSearchIdDto;
import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductStatDto;
import com.dianping.general.unified.search.api.generalsearchv2.request.DealGroupSearchRequest;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchAggRequest;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchRequestV2;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchStatRequest;
import com.dianping.general.unified.search.api.generalsearchv2.response.DealGroupSearchResponse;
import com.dianping.general.unified.search.api.sku.ProductSearchRequest;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import com.dianping.general.unified.search.api.sku.ProductSearchStatResponse;
import com.dianping.general.unified.search.api.sku.ProductShopSearchRequest;
import com.dianping.general.unified.search.api.sku.model.BrandProject;
import com.dianping.general.unified.search.api.sku.model.BrandProjectShop;
import com.dianping.gis.remote.dto.HotRegionStationDTO;
import com.dianping.gis.remote.dto.RegionInfoDTO;
import com.dianping.gis.remote.enums.RegionType;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gm.marketing.times.card.api.dto.TimesCardDetailExposureDTO;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityResponse;
import com.dianping.gmkt.activity.api.request.*;
import com.dianping.gmkt.activity.api.response.*;
import com.dianping.gmkt.activity.api.web.dto.request.MTPageDataRequest;
import com.dianping.gmkt.activity.api.web.dto.request.PageDataRequest;
import com.dianping.gmkt.activity.api.web.dto.response.DPGetDealResponse;
import com.dianping.gmkt.activity.api.web.dto.response.MTGetDealResponse;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityDTO;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityRequest;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.pool.dto.PoolAwardInfoDTO;
import com.dianping.gmkt.event.api.pool.dto.PoolAwardQueryRequest;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.home.service.api.ServiceResponse;
import com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO;
import com.dianping.joy.booking.api.generalpool.dto.PageInfo;
import com.dianping.joy.booking.api.generalpool.request.GeneralQueryPoolInfoRequest;
import com.dianping.joy.category.process.api.dto.technician.GetInfoByIDReqDTO;
import com.dianping.joy.category.process.api.dto.technician.biz.TechnicianDTO;
import com.dianping.joy.fitness.api.dto.thirdshop.GetShopInfoReqDTO;
import com.dianping.joy.fitness.api.dto.thirdshop.GetShopInfoRespDTO;
import com.dianping.joy.product.api.dto.VenueGreyDTO;
import com.dianping.joy.product.api.dto.venue.GetVenueProductByShopReqDTO;
import com.dianping.joy.product.api.dto.venue.ProductDTO;
import com.dianping.joy.solution.dto.JoySolutionResponse;
import com.dianping.joy.stock.api.dto.coursestock.CourseStockDTO;
import com.dianping.joy.stock.api.dto.coursestock.QueryStockReqDTO;
import com.dianping.joygeneral.api.sharerelation.dto.*;
import com.dianping.joygeneral.api.thirdpart.dto.QueryAutoOpenTableReqDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsQueryDTO;
import com.dianping.ktv.shop.api.protocol.ShopResponse;
import com.dianping.ktv.shop.coop.dto.CoopShopConfigDTO;
import com.dianping.ktv.shop.coop.request.CoopShopConfigQueryRequest;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.mobileossapi.dto.operate.ShortUrlRequest;
import com.dianping.mobileossapi.dto.operate.ShortUrlResult;
import com.dianping.pay.order.service.query.dto.BatchQueryOrderDTO;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.poi.mtDto.MtLocationDTO;
import com.dianping.product.shelf.common.dto.*;
import com.dianping.product.shelf.common.dto.activity.ActivityShelfToCDTO;
import com.dianping.product.shelf.common.request.*;
import com.dianping.product.shelf.common.request.activities.ProductQueryByShopIdToCRequest;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.shopremote.remote.dto.ShopUuidDTO;
import com.dianping.takeaway.open.shop.dto.TakeawayShopLongIdMapping;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.tpfun.checkout.api.dto.ShopReserveConfigDTO;
import com.dianping.tpfun.checkout.api.request.ShopReserveConfigRequest;
import com.dianping.tpfun.product.api.category.model.ProductCategory;
import com.dianping.tpfun.product.api.common.IResponse;
import com.dianping.tpfun.product.api.ktv.dto.KTVReserveAbstract;
import com.dianping.tpfun.product.api.ktv.dto.KTVSkuInfo;
import com.dianping.tpfun.product.api.ktv.request.GetKTVSkuInfoRequest;
import com.dianping.tpfun.product.api.ktv.request.QueryReserveAbstractByShopRequest;
import com.dianping.tpfun.product.api.newktv.dto.KTVProductDTO;
import com.dianping.tpfun.product.api.newktv.dto.KTVProductItemListDTO;
import com.dianping.tpfun.product.api.newktv.dto.KTVRuleDTO;
import com.dianping.tpfun.product.api.newktv.request.GetKTVProductRequest;
import com.dianping.tpfun.product.api.newktv.request.QueryKTVPoiProductItemsRequest;
import com.dianping.tpfun.product.api.newktv.request.QueryKTVPoiRulesRequest;
import com.dianping.tpfun.product.api.sku.abstracts.dto.SpuTypeAbstractDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.SimpleShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductPageDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductNearestShopReq;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetStandardProductPriceRequest;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.model.SpuShop;
import com.dianping.tpfun.product.api.sku.platform.resource.dto.ResourceInfoDTO;
import com.dianping.tpfun.product.api.sku.platform.resource.request.BatchQueryResourceInfoRequest;
import com.dianping.tpfun.product.api.sku.request.GetProductItemByProductRequest;
import com.dianping.tpfun.product.api.sku.request.QueryShopProductRequest;
import com.dianping.tpfun.product.api.sku.request.SpuShopRequest;
import com.dianping.tpfun.product.api.stocklogic.dto.FindStockScheduleRequest;
import com.dianping.tpfun.product.api.stocklogic.dto.resource.QueryProductCanBookingRequest;
import com.dianping.tpfun.sku.snapshot.api.dto.ProductSnapshotDTO;
import com.dianping.tpfun.sku.snapshot.api.request.QueryProductSnapshotRequest;
import com.dianping.tpfun.skuping.api.make.dto.PinRelationDTO;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.UserRequestPara;
import com.dianping.ugc.review.remote.dto.ExtParam;
import com.dianping.ugc.review.remote.dto.FilterParam;
import com.dianping.ugc.review.remote.dto.MTQueryResult;
import com.dianping.userremote.base.dto.NobleUserProfileDTO;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.userremote.dto.collection.FavorDTO;
import com.dianping.wed.business.weddingshopbrief.dto.WeddingShopBriefInfoDTO;
import com.dp.arts.client.request.Request;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.maoyan.shplatform.content.api.mthomepage.base.ProjectContentDTO;
import com.meituan.ads.as.thrift.AdsRequest;
import com.meituan.ads.as.thrift.AdsResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mpproduct.general.trade.api.dto.SpuProductDTO;
import com.meituan.mpproduct.general.trade.api.request.CitySpuIdQueryRequest;
import com.meituan.mpproduct.general.trade.api.request.PoiSpuIdQueryRequest;
import com.meituan.mpproduct.general.trade.api.request.QuerySpuProductRequest;
import com.meituan.service.deal.dealbasic.thrift.DealBasicInfo;
import com.meituan.service.mobile.group.geo.bean.AreaInfo;
import com.meituan.service.mobile.group.geo.bean.SubwayLine;
import com.meituan.service.mobile.group.geo.bean.SubwayStation;
import com.meituan.service.user.thrift.message.UserFields;
import com.meituan.service.user.thrift.message.UserModel;
import com.sankuai.backroombook.api.backroom.dto.BackroomReservationDTO;
import com.sankuai.backroombook.api.backroom.dto.MoreReservationReqDTO;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.drama.DramaPageReviewDetailDto;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.AggOverallReviewModel;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.request.ContentBaseRequest;
import com.sankuai.beautycontent.beautylaunchapi.model.enums.ConditionEnum;
import com.sankuai.beautycontent.beautylaunchapi.model.request.BeautyGoodsReviewRequest;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.request.QueryEduTechnicianVideoForCRequest;
import com.sankuai.beautycontent.intention.dto.IntentionResultResponse;
import com.sankuai.beautycontent.intention.request.IntentionRequest;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardDTO;
import com.sankuai.dzcard.joycard.navigation.api.dto.LoadProductShelfJoyCardReqDTO;
import com.sankuai.dzcard.joycard.navigation.api.dto.ProductShelfJoyCardDTO;
import com.sankuai.dzcard.navigation.api.dto.*;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshop.intervention.dto.ShopInterventionRequest;
import com.sankuai.dzshop.intervention.dto.ShopInterventionResponse;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductDTO;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.dztheme.generalproduct.ymtheme.req.ThemePlanRequest;
import com.sankuai.dztheme.generalproduct.ymtheme.res.PetMallProductThemeDTO;
import com.sankuai.dztheme.generalproduct.ymtheme.res.ThemePlanResponse;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import com.sankuai.dztheme.spuproduct.res.SpuDTO;
import com.sankuai.dzviewscene.intention.service.req.IntentionRecognizeRequest;
import com.sankuai.dzviewscene.intention.service.res.IntentionRecognizeResult;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productdegrade.req.ShelfDegradeRequest;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.req.OperatorShelfConfigFlashRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.NewReserveSubmissionPageWhiteShopCheckRespDTO;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.request.BNPLExposureRequest;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.health.sc.api.thrift.FoodInputParam;
import com.sankuai.health.sc.api.thrift.PoiFoodResponse;
import com.sankuai.health.sc.api.thrift.SpuTagProductParam;
import com.sankuai.health.sc.api.thrift.SpuTagResponse;
import com.sankuai.interest.core.thrift.remote.dto.InterestBatchCalculateResponse;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateRequest;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import com.sankuai.joynav.rb.spi.req.ListingRBReqDTO;
import com.sankuai.joynav.rb.spi.resp.ListingRBItem;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.process.thrift.dto.leads.LeadsCountReqDTO;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeResponse;
import com.sankuai.mdp.dzshoplist.rank.api.request.EntityScenesRankInfoRequest;
import com.sankuai.mdp.dzshoplist.rank.api.response.dto.RankEntityScenesDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ShopAutoVerifyQueryRequest;
import com.sankuai.merchantcard.timescard.exposure.dto.TimesCardSaysDTO;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByProductIdRequest;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByShopRequest;
import com.sankuai.mpcontent.feeds.thrift.dto.request.QueryIsLivingReqDTO;
import com.sankuai.mpcontent.feeds.thrift.dto.response.QueryIsLivingRespDTO;
import com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedEntityReq;
import com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedEntityResp;
import com.sankuai.mpmctexhibit.query.response.GetRelatedEntityReq;
import com.sankuai.mpmctexhibit.query.response.GetRelatedEntityResp;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.ListAllEffectMemberGrouponByTenantIdReqDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.ListAllEffectMemberGrouponByTenantIdRespDTO;
import com.sankuai.mpmctpoiext.info.query.thrift.platform.dto.PoiExtInfoQueryOneGateWayReqDTO;
import com.sankuai.mpmctpoiext.info.query.thrift.platform.dto.PoiExtInfoQueryOneGateWayRespDTO;
import com.sankuai.mppack.api.client.request.CombineProductInfoRequest;
import com.sankuai.mppack.api.client.request.ProductInfoRequest;
import com.sankuai.mppack.api.client.response.CombineProductInfoResponse;
import com.sankuai.mppack.api.client.response.ProductInfoResponse;
import com.sankuai.mppack.product.client.query.dto.ProductRelationDTO;
import com.sankuai.mppack.product.client.query.request.PackProductByIdRequest;
import com.sankuai.mpproduct.trade.api.model.ProductSkuDTO;
import com.sankuai.mpproduct.trade.api.request.ProductSkuBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.request.ResourceBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.response.ResourceBatchQueryResponse;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.DpPoiUuidRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.technician.trade.api.product.dto.TechStandardShelfModule;
import com.sankuai.technician.trade.api.product.request.TechProductShelfRequest;
import com.sankuai.user.collection.client.CollTypeEnum;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealRequest;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealResponse;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.ThirdUserInfo;
import org.apache.thrift.TException;

import java.util.*;
import java.util.concurrent.CompletableFuture;

public class CompositeAtomServiceMock implements CompositeAtomService {

    @Override
    public CompletableFuture<List<ResourceInfoDTO>> batchQueryResourceInfo(BatchQueryResourceInfoRequest batchQueryResourceInfoRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, DealGroupAttributeDTO>> batchGetDealAttribute(List<Integer> dealGroupIds, List<String> attributes) {
        return null;
    }

    @Override
    public CompletableFuture<List<ShopM>> multiShopMListNew(ShopThemePlanRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Response<ActivityShelfToCDTO>> getActivityShelfResponseByShopId(ProductQueryByShopIdToCRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, Integer>> batchGetDealIdCategoryIdMap(List<Integer> dealIds) {
        return null;
    }

    @Override
    public CompletableFuture<List<IdMapper>> batchGetDealIdByMtId(List<Integer> mtDealIds) {
        return null;
    }

    @Override
    public CompletableFuture<HaimaResponse> getHaiMaResponse(HaimaRequest haimaRequest) {
        return null;
    }

    @Override
    public CompletableFuture<DealGroupShop> getDealNearestShopResult(DealGroupShopSearchRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<ServiceResponse<Boolean>> getIsCooperativeResponse(int shopId) {
        return null;
    }

    @Override
    public CompletableFuture<ServiceResponse<Boolean>> getIsCooperativeResponseL(Long shopId) {
        return null;
    }

    @Override
    public CompletableFuture<ShopDTO> loadShop(int shopId) {
        return null;
    }

    /**
     * 通过ShopUuid查询门店信息
     *
     * @param shopUuid
     * @return
     */
    @Override
    public CompletableFuture<ShopDTO> loadShop(String shopUuid) {
        return null;
    }

    @Override
    public CompletableFuture<List<DpPoiDTO>> findShopsByUuids(DpPoiUuidRequest dpPoiUuidRequest) {
        return null;
    }

    @Override
    public CompletableFuture<List<Integer>> batchGetDpByMtId(int mtShopId) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> batchGetDpByMtIdL(Long mtShopId) {
        return null;
    }

    /**
     * 根据美团ID获取对应的点评ID;如果是多个，第一个是主门店ID
     *
     * @param dpShopId
     * @return
     */
    @Override
    public CompletableFuture<List<Integer>> batchGetMtShopIdByDp(int dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> batchGetMtShopIdByDpL(Long dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<Long> getMtByDpPoiIdL(long dpPoiId) {
        return null;
    }

    @Override
    public CompletableFuture<Long> getDpByMtPoiIdL(long dpPoiId) {
        return null;
    }

    @Override
    public CompletableFuture<List<DpPoiDTO>> findShopsByDpShopIds(DpPoiRequest dpPoiRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Response<ShelfDTO>> multiGetShelfNav(ShelfRequest navRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Response<ShelfNavTabProductList>> multiGetProductList(ShelfNavTabProductRequest
                                                                                           request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, List<Product>>> mGetBaseProductsByShop(QueryShopProductRequest request) {
        Product testProduct = new Product();
        testProduct.setSpuType(10001);
        testProduct.setId(2000);
        Map<Integer, List<Product>> shopIdToProductMap = Maps.newHashMap();
        shopIdToProductMap.put(100, Lists.newArrayList(testProduct));
        return CompletableFuture.completedFuture(shopIdToProductMap);
    }

    @Override
    public CompletableFuture<Map<Long, List<Product>>> mGetBaseProductsByLongShop(QueryShopProductRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, List<ProductItem>>> mGetProductItemsByProducts(GetProductItemByProductRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<GeneralProductResult> queryGeneralProductTheme(GeneralProductRequest
                                                                                    generalProductRequest) {
        GeneralProductDTO testProduct = new GeneralProductDTO();
        testProduct.setProductId(2000);
        testProduct.setName("测试商品");
        testProduct.setMarketPrice("2000");
        testProduct.setSalePrice("1000");
        testProduct.setPurchase("已售罄");
        testProduct.setSaleTag("已售2");
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(Lists.newArrayList(testProduct));
        return CompletableFuture.completedFuture(generalProductResult);
    }

    @Override
    public CompletableFuture<Map<Integer, List<Integer>>> batchGetDpByMtIds(List<Integer> mtShopIds) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> batchGetDpByMtIdsL(List<Long> mtShopIds) {
        return null;
    }

    @Override
    public CompletableFuture<DealProductResult> queryDealProductTheme(DealProductRequest dealProductRequest) {
        return null;
    }


    @Override
    public CompletableFuture<Map<Integer, ShopOnlineDealGroup>> batchGetShopOnlineDealGroups
            (ShopOnlineDealGroupRequest shopOnlineDealGroupRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, ShopOnlineDealGroup>> batchGetLongShopOnlineDealGroups(ShopOnlineDealGroupRequest shopOnlineDealGroupRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Response<List<ShelfShopRecProduct>>> multiGetShopRecommendProducts
            (ShelfShopRecProductRequest request) {
        return null;
    }

    /**
     * 根据点评Id查询团单Id映射
     *
     * @param dpDealIds
     * @return
     */
    @Override
    public CompletableFuture<List<IdMapper>> batchGetDealIdByDpId(List<Integer> dpDealIds) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, Long>> getMtDealIdByDp(List<Long> dpDealIds){
        return null;
    }

    /**
     * 获取团购/预订玩乐卡模块信息
     *
     * @param request
     * @return
     */
    @Override
    public CompletableFuture<ProductShelfJoyCardDTO> getCardModuleForProductShelf(LoadProductShelfJoyCardReqDTO
                                                                                          request) {
        return null;
    }

    @Override
    public CompletableFuture<BatchQueryShopActivityResponse> getShopActivity(BatchQueryShopActivityRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, List<Long>>> findShopIdsByProductIdsPoiMigrate(List<Integer> productIds) {
        return null;
    }

    @Override
    public CompletableFuture<ShopRefundConfigDTO> loadShopRefundConfig(ShopRefundConfigRequest shopRefundConfigRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Integer> loadDpShopId(int shopId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<Long> loadDpShopIdPoiMigrate(long shopId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<Long> loadMtShopId(long shopId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<Long> loadMtShopIdPoiMigrate(long shopId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<ProductSearchResponse<ProductSearchIdDto>> searchProductServiceV2
            (ProductSearchRequestV2 productSearchRequestV2) {
        return null;
    }

    /**
     * 获取dp侧ugc
     *
     * @param dpShopId
     * @param stars
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public CompletableFuture<Object> multiGetDpReviewByShopId(int dpShopId, List<Integer> stars, int page,
                                                              int pageSize) {
        return null;
    }

    @Override
    public CompletableFuture<Object> multiGetDpReviewByShopIdPoiMigrate(long dpShopId, List<Integer> stars, int page, int pageSize) {
        return null;
    }

    /**
     * 获取点评侧Ugc的数量
     *
     * @param dpShopId
     * @return
     */
    @Override
    public CompletableFuture<Map<String, Integer>> findDpReviewCountAllByStar(int dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<Map<String, Integer>> findDpReviewCountAllByStarPoiMigrate(Long dpShopId) {
        return null;
    }

    /**
     * 获取点评侧用户信息
     *
     * @param dpUserIds
     * @return
     */
    @Override
    public CompletableFuture<Map<Long, UserDTO>> batchGetUserMap(List<Long> dpUserIds) {
        return null;
    }

    /**
     * 点评侧，根据reviewId查询匿名评价的用户信息，如果不是匿名评价不返回
     *
     * @param mainIds         匿名评价的reviewId
     * @param userRequestPara 查询匿名评价作者信息的参数
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, AnonymousUserInfo>> batchGetAnonymousUserInfo
    (List<Integer> mainIds, UserRequestPara userRequestPara) {
        return null;
    }

    /**
     * 获取泛商品的适用商户列表
     *
     * @param productIds
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, List<Integer>>> batchGetAvailableShopIds(List<Integer> productIds) {
        return null;
    }

    /**
     * 获取商户IM的C端入口是否展示以及跳转链接
     *
     * @param reqDTO
     * @return
     */
    @Override
    public CompletableFuture<ClientEntryDTO> getImInfo(ClientEntryReqDTO reqDTO) {
        return null;
    }

    /**
     * 查找商品最近门店
     *
     * @param req
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, SimpleShopDTO>> batchGetProductNearestShop(GetProductNearestShopReq req) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, List<SpuTypeAbstractDTO>>> batchGetSpuTypeAbstractsByShopIds
            (List<Integer> doShopIds) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, List<SpuTypeAbstractDTO>>> batchGetSpuTypeAbstractsByLongShopIds(List<Long> doShopIds) {
        return null;
    }

    @Override
    public CompletableFuture<CardResponse<Map<Integer, TimesCardDetailExposureDTO>>> batchQueryTimesCards(QueryTimesCardByProductIdRequest queryTimesCardByProductIdRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Map<com.dianping.pay.promo.display.api.dto.Product, List<PromoDisplayDTO>>> batchQueryPromoDisplay
            (BatchQueryPromoDisplayRequest batchQueryPromoDisplayRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, List<ShelfShopRecProduct>>> multiGetShopRecommendProducts
            (BatchShelfShopRecommendRequest batchShelfShopRecommendRequest) {
        return null;
    }

    @Override
    public CompletableFuture<Integer> getDpCityIdByMt(Integer mtCityId) {
        return null;
    }

    @Override
    public CompletableFuture<Integer> getMtCityIdByDp(int dpCityId) {
        return null;
    }

    @Override
    public CompletableFuture<CardHoldStatusDTO> getCardHoldStatus(FindCardHoldStatusReqDTO request) {
        return null;
    }

    public CompletableFuture<com.dp.arts.client.response.Response> search(Request request) {
        return null;
    }

    @Override
    public CompletableFuture<ProductSearchResponse<com.dianping.general.unified.search.api.sku.model.Product>> commonSearchProduct
            (ProductSearchRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<ShopResponse<List<CoopShopConfigDTO>>> findShopConfigList(CoopShopConfigQueryRequest
                                                                                               request) {
        return null;
    }

    @Override
    public CompletableFuture<List<ShopDTO>> findShops(List<Integer> shopIds) {
        return null;
    }

    @Override
    public CompletableFuture<PageInfo<GeneralPoolInfoDTO>> pageFindPoolInfo(GeneralQueryPoolInfoRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<RecommendResult<RecommendDTO>> getRecommendResult(RecommendParameters recommendParameters) {
        int bizId = recommendParameters.getBizId();
        if (bizId == 114) {
            //热门剧本召回
            return CompletableFuture.completedFuture(buildRecommendResult(Lists.newArrayList("5301635", "5304586", "5302663", "5303673")));
        }
        return null;
    }

    private RecommendResult<RecommendDTO> buildRecommendResult(List<String> resultIds) {
        List<RecommendDTO> recommendDTOS = Lists.newArrayList();
        resultIds.forEach(resultId -> {
            RecommendDTO recommendDTO = new RecommendDTO();
            recommendDTO.setItem(resultId);
            recommendDTOS.add(recommendDTO);
        });
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        recommendResult.setBizData(new HashMap<String, Object>() {{
            put("recallSize", 99);
        }});
        recommendResult.setTotalSize(resultIds.size());
        recommendResult.setSortedResult(recommendDTOS);
        return recommendResult;
    }

    @Override
    public CompletableFuture<List<com.sankuai.wpt.user.retrieve.thrift.message.UserModel>> findUserInfoByMtUserIds(List<Long> mtUserIds, com.sankuai.wpt.user.retrieve.thrift.message.UserFields fields) {
        return null;
    }

    @Override
    public CompletableFuture<List<ProjectContentDTO>> mGetMaoyanProductDetail(List<Integer> projectIdList) {
        return null;
    }

    @Override
    public CompletableFuture<StandardProductPageDTO> getSpuById(long spuId) {
        return null;
    }

    @Override
    public CompletableFuture<StandardProductDTO> getSpuShop(long spuId, int cityId) {
        return null;
    }

    @Override
    public CompletableFuture<StandardProductDTO> getSpuPrice(GetStandardProductPriceRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Result<QueryData>> getSwanDataByKey(Integer bizTypeId, String queryKey, SwanParam swanParam) {
        return null;
    }

    @Override
    public CompletableFuture<Result<QueryData>> getSwanDataByQueryKey(Integer bizTypeId, String queryKey, SwanParam swanParam) {
        return null;
    }

    @Override
    public CompletableFuture<List<Integer>> getTransformProductId(List<Integer> dealIds) {
        return null;
    }

    @Override
    public CompletableFuture<HotRegionStationDTO> multiGetDpHotRegionList(int dpCityId) {
        return null;
    }

    @Override
    public CompletableFuture<List<AreaInfo>> multiGetMtAreaInfoByCityId(int mtCityId) {
        return null;
    }

    @Override
    public CompletableFuture<List<RegionInfoDTO>> multiGetDpChildRegionListByCityId(int cityId, RegionType regionType, int skip, int limit) {
        return null;
    }

    @Override
    public CompletableFuture<List<RegionInfoDTO>> batchGetDpRegionInfoList(List<Integer> dpRegionIdList) {
        return null;
    }

    @Override
    public CompletableFuture<Response<ShelfNavTabProductList>> getMultiSelectedShelfProducts(QueryProductsRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Response<ShelfFilterComponent>> getMultiSelectedShelfFilter(QueryFiltersRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> getIsGreyWhiteShop(Long shopId, String greyScene) {
        return null;
    }

    @Override
    public CompletableFuture<BindRelationResp> getVirtualDPUserIdByRealMTUserId(long realMtUserId) throws TException {
        return null;
    }

    @Override
    public CompletableFuture<ResourceBatchQueryResponse> batchQueryResource(ResourceBatchQueryRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, AggOverallReviewModel>> queryReview(ContentBaseRequest request) {
        List<Long> resourceIds = request.getParamValue(ConditionEnum.JU_BEN_SHA_DRAMA_IDS);
        return CompletableFuture.completedFuture(buildReviewResult(resourceIds));
    }

    private Map<Long, AggOverallReviewModel> buildReviewResult(List<Long> resourceIds) {
        AggOverallReviewModel reviewModel = new AggOverallReviewModel();
        reviewModel.setOverallScore("9.1");
        reviewModel.setOverallStar("4.55");
        reviewModel.setUrl("https://g.51ping.com/app/domino/gc-drama/drama-ugc.html?playId=" + resourceIds.get(0));
        reviewModel.setStarCountMap(new HashMap<String, String>() {{
            put("10", "35");
            put("30", "20");
            put("50", "45");
        }});
        Map<Long, AggOverallReviewModel> reviewModelMap = Maps.newHashMap();
        resourceIds.forEach(resourceId -> reviewModelMap.put(resourceId, reviewModel));
        return reviewModelMap;
    }

    @Override
    public CompletableFuture<Map<String, Boolean>> getDpFavorStatus(List<String> bizIds, int bizType, long userId) {
        return null;
    }

    @Override
    public CompletableFuture<Map<String, Integer>> getDpFavorCount(List<String> bizIds, int bizType) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> getMtCollectedFromGivenSets(long userId, CollTypeEnum collType, List<Long> ids, short srcType) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, Integer>> getMtCollectedCount(CollTypeEnum collType, List<Long> collIds, short srcType) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, Integer>> querySpuSaleByResourceId(long resourceId) {
        return null;
    }

    @Override
    public CompletableFuture<WeddingShopBriefInfoDTO> loadShopBriefInfoByShopId(long shopId) {
        return null;
    }

    @Override
    public CompletableFuture<List<BabyTimeLimeTgSpikeDTO>> loadBabyTimeLimitTgListByShopId(long dpShopId, int limit) {
        return null;
    }

    @Override
    public CompletableFuture<SpuTagResponse> batchGetMedicineShelfProducts(SpuTagProductParam spuTagProductParam) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, RankEntityScenesDTO>> loadRankEntityInfoDTO(EntityScenesRankInfoRequest entityScenesRankInfoRequest) {
        return null;
    }

    @Override
    public CompletableFuture<List<SubwayLine>> multiGetMtSubwayLineByCity(int mtCityId) {
        return null;
    }

    @Override
    public CompletableFuture<List<SubwayStation>> multiGetMtSubwayStationByLineId(int mtSubwayLineId) {
        return null;
    }

    @Override
    public CompletableFuture<List<Product>> findProductInfoByIds(List<Integer> productIds) {
        return null;
    }

    @Override
    public CompletableFuture<UnifiedOrderWithId> getUnifiedOrder(String unifiedOrderId) {
        return null;
    }

    @Override
    public CompletableFuture<ResponseDTO<Boolean>> getGroupPinShopBlock(Long dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<DramaPageReviewDetailDto> getStandardDetailReviewByResourceId(BeautyGoodsReviewRequest beautyGoodsReviewRequest) {
        return null;
    }

    @Override
    public CompletableFuture<List<SpuDTO>> querySpuTheme(SpuRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> querySpuIdsByCity(CitySpuIdQueryRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> querySpuIdsByPoi(PoiSpuIdQueryRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> checkGroupPinCouponStatus(int activityType) {
        return null;
    }

    @Override
    public CompletableFuture<QuerySecKillDealByPoiResponse> querySecKillDealByPoi(QuerySecKillDealByPoiRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchDealGroupBySearchTerm(DealGroupSearchRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Integer, DealDetailDto>> batchQueryDealDetailInfo(List<Integer> dealGroupIds) {
        return null;
    }

    @Override
    public CompletableFuture<String> getRecommendFlowId(RecommendParameters recommendParameters) {
        return null;
    }

    @Override
    public CompletableFuture<ListAllEffectMemberGrouponByTenantIdRespDTO> listAllEffectMemberGrouponByTenantId(ListAllEffectMemberGrouponByTenantIdReqDTO tenantIdReqDTO) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> queryMallFilteredDealGroups(List<Long> dealGroupIds, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<IntentionResultResponse> recognizeIntention(IntentionRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<QueryIsLivingRespDTO> queryIsLiving(QueryIsLivingReqDTO reqDTO) throws TException {
        return null;
    }

    @Override
    public CompletableFuture<ProductInfoResponse> batchQueryProductInfo(ProductInfoRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> queryShopSaleDealIds(long shopId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<List<DealGroupDTO>> batchGetMtDealIdByDp(List<Long> dpDealIds) {
        return null;
    }

    @Override
    public CompletableFuture<List<DealGroupDTO>> batchGetServiceProjectByDealIds(QueryByDealGroupIdRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<DealResponse> getFoodDealInfos(DealRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> batchGetMtByDpIds(List<Long> dpShopIds) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, MtPoiDTO>> batchGetPoiByMtShopIds(List<Long> mtShopIds, List<String> fields) {
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, DealBasicInfo>> getDealBasicInfo(List<Long> dealIds) {
        return null;
    }

    @Override
    public CompletableFuture<Result<QueryData>> batchQuerySupplyCommandMatchDeals(int backCityId, int pageNo, int pageSize) {
        return null;
    }

    @Override
    public CompletableFuture<TransformCityTypeResponse> queryBackCityId(String sourceCityId, boolean isMt) {
        return null;
    }

    @Override
    public CompletableFuture<CombineProductInfoResponse> batchQueryCombinationProductInfo(CombineProductInfoRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<FlattedBindRelation> getUserInfoByDpUserId(long dpUserId) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> checkZdcTagByDpShopId(long dpShopId, long zdcTag) {
        return null;
    }

    @Override
    public CompletableFuture<List<Long>> findZdcTagByDpShopId(long dpShopId, String bizCode) {
        return null;
    }

    @Override
    public CompletableFuture<List<EduTechnicianVideoDTO>> batchQueryEduTechnicianVideoInfo(QueryEduTechnicianVideoForCRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds(QueryByDealGroupIdRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<BatchQueryLeadsInfoRespDTO> batchQueryLeadsInfo(BatchQueryLeadsInfoReqDTO request) {
        return null;
    }

    @Override
    public CompletableFuture<LeadsCountRespDTO> queryShopReservationCount(LeadsCountADTO request) {
        return null;
    }

    @Override
    public CompletableFuture<String> queryMobile(long userId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<List<MemberCardDTO>> queryFitnessCrossCardListByUserId(String mobile, int platform, Set<Integer> statusSet, int rightPackageType) {
        return null;
    }

    @Override
    public CompletableFuture<List<ProductRelationDTO>> queryPackProductIdByShop(PackProductByIdRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<TechnicianResp<TechStandardShelfModule>> queryStaffShelf(TechProductShelfRequest reqDTO) {
        return null;
    }

    @Override
    public CompletableFuture<PromoQRCodeResponse<StaffCodeDTO>> getStaffCodeDTOByCodeId(Long codeId) {
        return null;
    }

    @Override
    public CompletableFuture<CommonResponse<List<DistributorActivityDTO>>> queryJoinDistributorActivity(DistributorActivityRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<com.dianping.joygeneral.api.thirdpart.dto.Response<Boolean>> queryAutoOpenTable(QueryAutoOpenTableReqDTO request) {
        return null;
    }

    @Override
    public CompletableFuture<RemoteResponse<List<Long>>> queryPromoCodeDealGroups(Long dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<List<SpuProductDTO>> batchQuerySpuRelatedProductIds(QuerySpuProductRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<List<ProductSkuDTO>> queryPlatformProducts(ProductSkuBatchQueryRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<List<ProductSkuDTO>> batchQueryPlatformProducts(ProductSkuBatchQueryRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Integer> queryLeadsCount(LeadsCountReqDTO reqDTO) {
        return null;
    }

    @Override
    public CompletableFuture<CardResponse<Map<Long, List<TimesCardDetailExposureDTO>>>> batchQueryTimesCardsByShop(QueryTimesCardByShopRequest timesCardByShopRequest) {
        return null;
    }

    @Override
    public CompletableFuture<IntentionRecognizeResult> recognize(IntentionRecognizeRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<String> queryCrossTitleByBackCat(List<Integer> backCatIds) {
        return null;
    }

    public CompletableFuture<List<Long>> queryShopSaleDealGroupIds(long shopId, int platform, int start, int limit) {
        return null;
    }

    public CompletableFuture<NewReserveSubmissionPageWhiteShopCheckRespDTO> querySelfOperatedCleaningShopInfo(Long dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> queryIsSupportStrictReserve(Long dpShopId) {
        return null;
    }

    @Override
    public CompletableFuture<InterestCalculateResponse> interestCalculate(InterestCalculateRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<List<InterestBatchCalculateResponse>> batchInterestCalculate(List<InterestCalculateRequest> requestList) {
        return null;
    }

    @Override
    public CompletableFuture<QueryResultSync> queryByKey(KeyQueryParamSync param) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> asyncFlashOperatorShelfConfig(OperatorShelfConfigFlashRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> queryIsMiniprogramShop(Long shopId, int platform) {
        return null;
    }

    @Override
    public CompletableFuture<BNPLExposureResponse> queryIsAfterPay(BNPLExposureRequest request) {
        return null;
    }

    @Override
    public CompletableFuture<Boolean> queryShopIsAutoVerify(ShopAutoVerifyQueryRequest request) {
        return null;
    }
}
