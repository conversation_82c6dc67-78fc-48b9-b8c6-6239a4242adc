package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.NationSubsidyPromoTagStrategy;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class NationSubsidyPromoTagBuildStrategyTest {

    @InjectMocks
    private NationSubsidyPromoTagBuildStrategy strategy;

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private ProductM productM;

    /**
     * 测试：当 req 为 null 时，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testBuildTagReqIsNull() throws Throwable {
        // act
        strategy.buildTag(null);
    }

    /**
     * 测试：当平台不支持国补时，返回 null
     */
    @Test
    public void testBuildTagPlatformNotSupport() throws Throwable {
        // arrange
        when(req.getPlatform()).thenReturn(1);

        // act
        DzTagVO result = strategy.buildTag(req);

        // assert
        assertNull(result);
    }

    /**
     * 测试：当产品没有国补时，返回 null
     */
    @Test
    public void testBuildTagProductNoSubsidy() throws Throwable {
        // arrange
        when(req.getPlatform()).thenReturn(2);
        when(req.getProductM()).thenReturn(productM);

        // act
        DzTagVO result = strategy.buildTag(req);

        // assert
        assertNull(result);
    }

    /**
     * 测试：当平台支持国补且产品有国补时，返回正确的 DzTagVO
     */
    @Test
    public void testBuildTagSuccess() throws Throwable {
        // arrange
        when(req.getPlatform()).thenReturn(2);
        when(req.getProductM()).thenReturn(productM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setNationalSubsidyPrice(BigDecimal.valueOf(100));
        when(productM.getPromoPrices()).thenReturn(Lists.newArrayList(productPromoPriceM));

        // act
        DzTagVO result = strategy.buildTag(req);

        // assert
        assertNotNull(result);
        assertEquals(ColorUtils.colorFFFFFF, result.getTextColor());
        assertEquals(ColorUtils.color00A72D, result.getBackground());
        assertEquals("国补价约¥100", result.getText());
        assertEquals(1, result.getMultiText().size());
        assertEquals("国补价约¥100", result.getMultiText().get(0));
    }
}