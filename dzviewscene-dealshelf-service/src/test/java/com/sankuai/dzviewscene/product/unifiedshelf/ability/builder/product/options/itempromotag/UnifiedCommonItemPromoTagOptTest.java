package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempromotag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPromoTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedCommonItemPromoTagOpt的compute方法
 */
public class UnifiedCommonItemPromoTagOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemPromoTagVP.Param mockParam;
    @Mock
    private UnifiedCommonItemPromoTagOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private UnifiedCommonItemPromoTagOpt unifiedCommonItemPromoTagOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedCommonItemPromoTagOpt = new UnifiedCommonItemPromoTagOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * 测试团购次卡标签生成
     */
    @Test
    public void testComputeForDealTimesCardTag() {
        when(mockProductM.isTimesDeal()).thenReturn(true);
        when(mockProductM.getAttr(anyString())).thenReturn("2");
        when(mockParam.getSalePrice()).thenReturn("100");

        List<RichLabelModel> result = unifiedCommonItemPromoTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("2次¥100", result.get(0).getText());
    }

    /**
     * 测试买贵必赔标签生成
     */
    @Test
    public void testComputeForPriceGuaranteeTag() {
        when(mockProductM.isTimesDeal()).thenReturn(false);
        when(mockProductM.getAttr("bestPriceGuaranteeTag")).thenReturn("true");

        List<RichLabelModel> result = unifiedCommonItemPromoTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("买贵必赔", result.get(0).getText());
    }

    /**
     * 测试折扣标签生成
     */
    @Test
    public void testComputeForDiscountTag() {
        when(mockProductM.isTimesDeal()).thenReturn(false);
        when(mockParam.getSalePrice()).thenReturn("5");
        when(mockProductM.getMarketPrice()).thenReturn("10");

        List<RichLabelModel> result = unifiedCommonItemPromoTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.get(0).getText().contains("折"));
    }

    /**
     * 测试折扣标签附加信息
     */
    @Test
    public void testComputeForDiscountTagAppendAttr() {
        when(mockProductM.isTimesDeal()).thenReturn(false);
        when(mockParam.getSalePrice()).thenReturn("5");
        when(mockProductM.getMarketPrice()).thenReturn("10");
        when(mockConfig.getAppendAttrKeys()).thenReturn(Lists.newArrayList("attr"));
        when(mockProductM.getAttr("attr")).thenReturn("￥5/币");

        List<RichLabelModel> result = unifiedCommonItemPromoTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertFalse(CollectionUtils.isEmpty(result.get(0).getMultiText()));
        assertEquals(result.get(0).getMultiText(), Lists.newArrayList("5.0折","￥5/币"));
    }

    /**
     * 测试无标签生成
     */
    @Test
    public void testComputeForNoTag() {
        when(mockProductM.isTimesDeal()).thenReturn(false);
        when(mockParam.getSalePrice()).thenReturn(null);

        List<RichLabelModel> result = unifiedCommonItemPromoTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNull(result);
    }

    @Test
    public void testFilterByProductAttr() throws Exception {
        // 创建测试对象
        UnifiedCommonItemPromoTagOpt opt = new UnifiedCommonItemPromoTagOpt();

        // 创建测试数据
        List<DouHuM> douHuMList = Lists.newArrayList();
        ProductM productM = new ProductM();
        UnifiedCommonItemPromoTagOpt.Config config = new UnifiedCommonItemPromoTagOpt.Config();

        // 使用反射获取私有方法
        Method method = UnifiedCommonItemPromoTagOpt.class.getDeclaredMethod("filterByProductAttr", List.class, ProductM.class, UnifiedCommonItemPromoTagOpt.Config.class);
        method.setAccessible(true);

        // 测试场景1: config.getFilterByAttr() 为空
        Map<String, List<String>> filterByAttr = new HashMap<>();
        config.setFilterByAttr(filterByAttr);
        boolean result = (boolean) method.invoke(opt, douHuMList, productM, config);
        assertFalse(result);

        // 测试场景2: config.getFilterByAttr() 不为空，但 productM 的 attr 不匹配
        filterByAttr.put("key1", Lists.newArrayList("value1"));
        config.setFilterByAttr(filterByAttr);
        productM.setAttr("key1", "value2");
        result = (boolean) method.invoke(opt, douHuMList, productM, config);
        assertFalse(result);
    }
}
