package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.product.shelf.common.enums.NavTagTypeEnum;
import com.dianping.product.shelf.common.request.ShelfRequest;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.ShelfCacheGreyConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfFilterUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class AbstractShelfFilterFetcherTest {

    private ActivityContext activityContext = mock(ActivityContext.class);

    private ShelfRequest shelfRequest = mock(ShelfRequest.class);

    private FlagshipStoreM flagshipStoreM = mock(FlagshipStoreM.class);

    private AbstractShelfFilterFetcher abstractShelfFilterFetcher = Mockito.mock(AbstractShelfFilterFetcher.class, Mockito.CALLS_REAL_METHODS);

    @InjectMocks
    private AbstractShelfFilterFetcher filterFetcher = new AbstractShelfFilterFetcher() {

        @Override
        public CompletableFuture<Map<String, FilterM>> build(ActivityContext ctx) {
            return null;
        }
    };

    @Mock
    private FilterFetcherExt filterFetcherExt;

    @Mock
    private com.sankuai.dzviewscene.shelf.framework.ComponentFinder componentFinder;

    @Mock
    private ActivityContext mockActivityContext;

    @Mock
    private ShelfCacheGreyConfig filterGreyConfig;

    @Before
    public void setUp() {
        // Mock the componentFinder to return our mocked filterFetcherExt for any findExtPoint call
        when(componentFinder.findExtPoint(any(ActivityContext.class), eq(FilterFetcherExt.EXT_POINT_FILTER_CODE))).thenReturn(filterFetcherExt);
    }

    @Test
    public void testPaddingShelfTypeAndStoreIdOfFlagshipStoreShelf_ShelfRequestIsNull() throws Throwable {
        abstractShelfFilterFetcher.paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(null, activityContext);
        verify(shelfRequest, never()).setShelfType(any());
        verify(shelfRequest, never()).setStoreId(anyLong());
    }

    @Test
    public void testPaddingShelfTypeAndStoreIdOfFlagshipStoreShelf_NotFlagshipStore() throws Throwable {
        when(activityContext.getSceneCode()).thenReturn("NON_FLAGSHIP_STORE_SCENE");
        abstractShelfFilterFetcher.paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(shelfRequest, activityContext);
        verify(shelfRequest, never()).setShelfType(any());
        verify(shelfRequest, never()).setStoreId(anyLong());
    }

    @Test
    public void testBuildShelfRequest_mt() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        ctx.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
        ctx.addParam(ShelfActivityConstants.Params.userAgent, VCPlatformEnum.MT.getType());
        ctx.addParam(ShelfActivityConstants.Params.clientType, "ios");
        ctx.addParam(ShelfActivityConstants.Params.mtUserId, 12345678909L);
        ShelfFilterFetcher fetcher = new ShelfFilterFetcher();
        ShelfRequest shelfRequest = fetcher.buildShelfRequest(ctx);
        assertEquals(12345678909L, shelfRequest.getUserId());
    }

    @Test
    public void testBuildShelfRequest_dp() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        ctx.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
        ctx.addParam(ShelfActivityConstants.Params.userAgent, VCPlatformEnum.DP.getType());
        ctx.addParam(ShelfActivityConstants.Params.clientType, "ios");
        ctx.addParam(ShelfActivityConstants.Params.dpUserId, 12345678909L);
        ShelfFilterFetcher fetcher = new ShelfFilterFetcher();
        ShelfRequest shelfRequest = fetcher.buildShelfRequest(ctx);
        assertEquals(12345678909L, shelfRequest.getUserId());
    }

    @Test
    public void testGetSelectedWhenSelectedFilterIdExists() throws Throwable {
        // Setup mocks and test data here
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.selectedFilterId, "123");
        long result = filterFetcher.getSelected("testGroup", new FilterM(), activityContext);
        assertEquals(123L, result);
    }

    @Test
    public void testGetSelectedWhenSelectedFilterIdInvalid() throws Throwable {
        // Setup mocks and test data here
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.selectedFilterId, "0");
        String groupName = "testGroup";
        FilterM filterM = new FilterM();
        long result = filterFetcher.getSelected(groupName, filterM, activityContext);
        assertEquals(0L, result);
    }

    @Test
    public void testGetSelectedWhenSelectedFilterIdNotExists() throws Throwable {
        // Setup mocks and test data here
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        FilterM filterM = new FilterM();
        long result = filterFetcher.getSelected(groupName, filterM, activityContext);
        assertEquals(0L, result);
    }

    @Test
    public void testGetSelectedWhenExtPointNotFound() throws Throwable {
        // Setup mocks and test data here
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        FilterM filterM = new FilterM();
        long result = filterFetcher.getSelected(groupName, filterM, activityContext);
        assertEquals(0L, result);
    }

    @Test
    public void test_setSecondaryFilter() throws Throwable {
        List<String> selectedTag = Lists.newArrayList("implant_amount1", "doctor_expert");
        String filterJson = "[{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200129277,\"multiSelect\":false,\"selected\":false,\"title\":\"全部\",\"totalCount\":64,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":100001,\"multiSelect\":false,\"selected\":false,\"title\":\"神券\",\"totalCount\":61,\"type\":0},{\"activity\":false,\"children\":[{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131383,\"multiSelect\":false,\"selected\":false,\"title\":\"全部\",\"totalCount\":4,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131384,\"multiSelect\":false,\"selected\":false,\"title\":\"周末可用\",\"totalCount\":3,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131385,\"multiSelect\":false,\"selected\":false,\"title\":\"含喷砂\",\"totalCount\":1,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131386,\"multiSelect\":false,\"selected\":false,\"title\":\"儿童洗牙\",\"totalCount\":1,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131388,\"multiSelect\":false,\"selected\":false,\"title\":\"瑞士GBT\",\"totalCount\":1,\"type\":0}],\"depth\":1,\"filterId\":200127903,\"multiSelect\":false,\"selected\":false,\"title\":\"洗牙\",\"totalCount\":10,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127906,\"multiSelect\":false,\"selected\":false,\"title\":\"矫正\",\"totalCount\":5,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127909,\"filterOptionTree\":{\"children\":[{\"children\":[{\"children\":[],\"filterId\":200127909,\"identityName\":\"implant_amount1\",\"multipleSelect\":false,\"selected\":false,\"showName\":\"单颗\"}],\"filterId\":200127909,\"identityName\":\"tooth_count\",\"multipleSelect\":false,\"selected\":false,\"showName\":\"数量\"},{\"children\":[{\"children\":[],\"filterId\":200127909,\"identityName\":\"dental_crown_brand7\",\"multipleSelect\":false,\"selected\":false,\"showName\":\"中国爱尔创\"}],\"filterId\":200127909,\"identityName\":\"dental_crown\",\"multipleSelect\":false,\"selected\":false,\"showName\":\"牙冠\"},{\"children\":[{\"children\":[],\"filterId\":200127909,\"identityName\":\"doctor_expert\",\"multipleSelect\":false,\"selected\":false,\"showName\":\"种植专家\"}],\"filterId\":200127909,\"identityName\":\"doctor\",\"multipleSelect\":false,\"selected\":false,\"showName\":\"医生\"}],\"filterId\":200127909,\"multipleSelect\":false,\"selected\":false},\"multiSelect\":false,\"selected\":false,\"title\":\"种植牙\",\"totalCount\":18,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127904,\"multiSelect\":false,\"selected\":false,\"title\":\"补牙\",\"totalCount\":5,\"type\":0},{\"activity\":false,\"children\":[{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131389,\"multiSelect\":false,\"selected\":false,\"title\":\"全部\",\"totalCount\":14,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131390,\"multiSelect\":false,\"selected\":false,\"title\":\"周末可用\",\"totalCount\":14,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131391,\"multiSelect\":false,\"selected\":false,\"title\":\"涂氟\",\"totalCount\":1,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131392,\"multiSelect\":false,\"selected\":false,\"title\":\"窝沟封闭\",\"totalCount\":2,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131393,\"multiSelect\":false,\"selected\":false,\"title\":\"儿童补牙\",\"totalCount\":4,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131394,\"multiSelect\":false,\"selected\":false,\"title\":\"乳牙拔除\",\"totalCount\":1,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131395,\"multiSelect\":false,\"selected\":false,\"title\":\"儿童洗牙\",\"totalCount\":1,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131396,\"multiSelect\":false,\"selected\":false,\"title\":\"早期矫治\",\"totalCount\":4,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":2,\"filterId\":200131397,\"multiSelect\":false,\"selected\":false,\"title\":\"预成冠\",\"totalCount\":1,\"type\":0}],\"depth\":1,\"filterId\":200127905,\"multiSelect\":false,\"selected\":false,\"title\":\"儿童齿科\",\"totalCount\":42,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127907,\"multiSelect\":false,\"selected\":false,\"title\":\"拔牙\",\"totalCount\":4,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127908,\"multiSelect\":false,\"selected\":false,\"title\":\"美白\",\"totalCount\":5,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127910,\"multiSelect\":false,\"selected\":false,\"title\":\"烤瓷/全瓷牙\",\"totalCount\":4,\"type\":0},{\"activity\":false,\"children\":[],\"depth\":1,\"filterId\":200127911,\"multiSelect\":false,\"selected\":false,\"title\":\"其他治疗\",\"totalCount\":14,\"type\":0}]";
        List<FilterBtnM> filters = JsonCodec.converseList(filterJson, FilterBtnM.class);
        ActivityContext ctx = new ActivityContext();
        List<String> filterId = Lists.newArrayList("200127909");
        String s = ShelfFilterUtils.setSecondaryFilterActivityContext(filterId, selectedTag, filters, ctx);
        ActivityCxt activityCxt = new ActivityCxt();
        System.out.println(JSON.toJSONString(filters));
        Assert.assertNotNull(s);
    }

    /**
     * 测试isFShelf方法，当shelfTypeConfig为空时应返回false
     */
    @Test
    public void testIsFShelfWithEmptyShelfTypeConfig() throws Throwable {
        // arrange
        when(mockActivityContext.getParam(QueryFetcher.Params.recallFShelfConfig)).thenReturn(Collections.emptyMap());
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "isFShelf", mockActivityContext, new HashMap<String, FilterM>());
        // assert
        assertFalse(result);
    }

    /**
     * 测试isFShelf方法，当productNumThreshold和tabNumThreshold都小于等于0时应返回false
     */
    @Test
    public void testIsFShelfWithInvalidThresholds() throws Throwable {
        // arrange
        Map<String, Object> shelfTypeConfig = new HashMap<>();
        shelfTypeConfig.put("productNumThreshold", -1);
        shelfTypeConfig.put("tabNumThreshold", 0);
        when(mockActivityContext.getParam(QueryFetcher.Params.recallFShelfConfig)).thenReturn(shelfTypeConfig);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "isFShelf", mockActivityContext, new HashMap<String, FilterM>());
        // assert
        assertFalse(result);
    }

    /**
     * 测试isFShelf方法，当productNumThreshold和tabNumThreshold都小于等于0时应返回false
     */
    @Test
    public void testIsFShelfWithUnsufficientThresholds() throws Throwable {
        // arrange
        Map<String, Object> shelfTypeConfig = new HashMap<>();
        shelfTypeConfig.put("productNumThreshold", 1);
        shelfTypeConfig.put("tabNumThreshold", 1);
        when(mockActivityContext.getParam(QueryFetcher.Params.recallFShelfConfig)).thenReturn(shelfTypeConfig);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "isFShelf", mockActivityContext, new HashMap<String, FilterM>());
        // assert
        assertFalse(result);
    }

    /**
     * 测试hitExp方法，当shelfTypeConfig为空时应返回false
     */
    @Test
    public void testHitExpWithEmptyShelfTypeConfig() throws Throwable {
        // arrange
        ActivityContext ctx = new ActivityContext();
        ctx.getParameters().put(QueryFetcher.Params.recallFShelfConfig, new HashMap<>());
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "hitExp", ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试hitExp方法，当skList为空时应返回false
     */
    @Test
    public void testHitExpWithEmptySkList() throws Throwable {
        // arrange
        ActivityContext ctx = new ActivityContext();
        Map<String, Object> shelfTypeConfig = new HashMap<>();
        shelfTypeConfig.put("skList", com.google.common.collect.Lists.newArrayList());
        ctx.getParameters().put(QueryFetcher.Params.recallFShelfConfig, shelfTypeConfig);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "hitExp", ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试hitExp方法，当skList不为空且匹配时应返回true
     */
    @Test
    public void testHitExpWithMatchingSkList() throws Throwable {
        // arrange
        Map<String, Object> shelfTypeConfig = new HashMap<>();
        shelfTypeConfig.put("skList", com.google.common.collect.Lists.newArrayList("sk1"));
        when(mockActivityContext.getParam(QueryFetcher.Params.recallFShelfConfig)).thenReturn(shelfTypeConfig);
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk1");
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(Lists.newArrayList(douHuM));
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "hitExp", mockActivityContext);
        // assert
        assertTrue(result);
    }

    /**
     * 测试hitExp方法，当skList不为空但不匹配时应返回false
     */
    @Test
    public void testHitExpWithNonMatchingSkList() throws Throwable {
        // arrange
        Map<String, Object> shelfTypeConfig = new HashMap<>();
        shelfTypeConfig.put("skList", com.google.common.collect.Lists.newArrayList("sk1"));
        when(mockActivityContext.getParam(QueryFetcher.Params.recallFShelfConfig)).thenReturn(shelfTypeConfig);
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk2");
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(Lists.newArrayList(douHuM));
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "hitExp", mockActivityContext);
        // assert
        assertFalse(result);
    }

    /**
     * 测试getMainTabNum方法，当groupNames为空时
     */
    @Test
    public void testGetMainTabNumWithEmptyGroupNames() throws Throwable {
        Map<String, FilterM> filterMs = Maps.newHashMap();
        when(activityContext.getParam(QueryFetcher.Params.groupNames)).thenReturn(Lists.newArrayList());
        int result = (int) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "getMainTabNum", activityContext, filterMs);
        assertEquals(0, result);
    }

    /**
     * 测试getMainTabNum方法，当filterMs中不包含groupName时
     */
    @Test
    public void testGetMainTabNumWithGroupNameNotInFilterMs() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        ctx.getParameters().put("groupNames", Lists.newArrayList("groupName"));
        Map<String, FilterM> filterMs = Maps.newHashMap();
        int result = (int) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "getMainTabNum", ctx, filterMs);
        assertEquals(0, result);
    }

    /**
     * 测试getMainTabNum方法，当filterMs中包含groupName但filters为空时
     */
    @Test
    public void testGetMainTabNumWithEmptyFilters() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        ctx.getParameters().put("groupNames", Lists.newArrayList("groupName"));
        Map<String, FilterM> filterMs = Maps.newHashMap();
        FilterM filterM = new FilterM();
        filterMs.put("groupName", filterM);
        int result = (int) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "getMainTabNum", ctx, filterMs);
        assertEquals(0, result);
    }

    /**
     * 测试getMainTabNum方法，当filters不为空且包含NavTagTypeEnum.PROJECT_CATE类型时
     */
    @Test
    public void testGetMainTabNumWithValidFilters() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        ctx.getParameters().put("groupNames", Lists.newArrayList("groupName"));
        Map<String, FilterM> filterMs = Maps.newHashMap();
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setNavTagType(NavTagTypeEnum.PROJECT_CATEGORY_ALL_PRODUCT.getType());
        filterM.setFilters(Lists.newArrayList(filterBtnM));
        filterMs.put("groupName", filterM);
//        when(fetcher.getMainTabNum(ctx, filterMs)).thenCallRealMethod();
        int result = (int) ReflectionTestUtils.invokeMethod(abstractShelfFilterFetcher, "getMainTabNum", ctx, filterMs);
        assertEquals(1, result);
    }
}
