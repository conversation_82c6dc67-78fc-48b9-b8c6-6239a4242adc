package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/7
 */
@Ignore("没有可执行的方法")
@SpringBootTest
@RunWith(SpringRunner.class)
@ComponentScan("com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter")
public class MultipleConfigFilterFetcherExtTest {
    @Resource
    private MultipleConfigFilterFetcherExt filterFetcherExt;

    //@Test
    @DisplayName("未命中筛选栏")
    public void selectNon(){
        ActivityContext activityContext = new ActivityContext();
        activityContext.setSceneCode("life_poi_deal_filter_products_shelf");
        activityContext.addParam(ShelfActivityConstants.Params.keyword,"不会命中的词");
        String groupName = "团购";
        FilterM filterM = buildFilterM();
        long filterId = filterFetcherExt.selected(activityContext,groupName,filterM);
        Assert.assertEquals(filterId,200121964);
    }

    //@Test
    @DisplayName("命中筛选栏")
    public void selectTarget(){
        ActivityContext activityContext = new ActivityContext();
        activityContext.setSceneCode("life_poi_deal_filter_products_shelf");
        activityContext.addParam(ShelfActivityConstants.Params.keyword,"空调清洗");
        String groupName = "团购";
        FilterM filterM = buildFilterM();
        long filterId = filterFetcherExt.selected(activityContext,groupName,filterM);
        Assert.assertEquals(filterId,200122875);
    }

    private FilterM buildFilterM(){
        String str = "{\"extra\":{\"shelfTagsFromPlatform\":[\"随时退\",\"过期退\"]},\"filters\":[{\"children\":[],\"filterId\":200121964,\"selected\":false,\"title\":\"全部\",\"totalCount\":71,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200122834,\"selected\":false,\"title\":\"全部\",\"totalCount\":21,\"type\":0},{\"children\":[],\"filterId\":200122837,\"selected\":false,\"title\":\"洗衣机\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200122835,\"selected\":false,\"title\":\"空调\",\"totalCount\":4,\"type\":0},{\"children\":[],\"filterId\":200122839,\"selected\":false,\"title\":\"燃气灶\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200122836,\"selected\":false,\"title\":\"冰箱\",\"totalCount\":5,\"type\":0},{\"children\":[],\"filterId\":200122840,\"selected\":false,\"title\":\"热水器\",\"totalCount\":3,\"type\":0},{\"children\":[],\"filterId\":200122838,\"selected\":false,\"title\":\"电视机\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122842,\"selected\":false,\"title\":\"油烟机\",\"totalCount\":4,\"type\":0},{\"children\":[],\"filterId\":200122863,\"selected\":false,\"title\":\"饮水机\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122869,\"selected\":false,\"title\":\"净水器\",\"totalCount\":1,\"type\":0}],\"filterId\":200121965,\"selected\":false,\"title\":\"家电维修\",\"totalCount\":44,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200123259,\"selected\":false,\"title\":\"全部\",\"totalCount\":9,\"type\":0},{\"children\":[],\"filterId\":200123261,\"selected\":false,\"title\":\"马桶\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200123269,\"selected\":false,\"title\":\"地漏\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200123262,\"selected\":false,\"title\":\"面盆\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123263,\"selected\":false,\"title\":\"浴缸\",\"totalCount\":1,\"type\":0}],\"filterId\":200122058,\"selected\":false,\"title\":\"厨卫洁具\",\"totalCount\":15,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200123272,\"selected\":false,\"title\":\"全部\",\"totalCount\":7,\"type\":0},{\"children\":[],\"filterId\":200123276,\"selected\":false,\"title\":\"房屋防水\",\"totalCount\":6,\"type\":0},{\"children\":[],\"filterId\":200123273,\"selected\":false,\"title\":\"厨卫防水\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200123274,\"selected\":false,\"title\":\"阳台防水\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123275,\"selected\":false,\"title\":\"外墙防水\",\"totalCount\":1,\"type\":0}],\"filterId\":200122059,\"selected\":false,\"title\":\"防水补漏\",\"totalCount\":17,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200122874,\"selected\":false,\"title\":\"全部\",\"totalCount\":19,\"type\":0},{\"children\":[],\"filterId\":200122876,\"selected\":false,\"title\":\"油烟机清洗\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200122875,\"selected\":false,\"title\":\"空调清洗\",\"totalCount\":7,\"type\":0},{\"children\":[],\"filterId\":200122878,\"selected\":false,\"title\":\"洗衣机清洗\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200122882,\"selected\":false,\"title\":\"热水器清洗\",\"totalCount\":2,\"type\":0},{\"children\":[],\"filterId\":200122879,\"selected\":false,\"title\":\"冰箱清洗\",\"totalCount\":4,\"type\":0},{\"children\":[],\"filterId\":200122880,\"selected\":false,\"title\":\"饮水机清洗\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122877,\"selected\":false,\"title\":\"燃气灶清洗\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122881,\"selected\":false,\"title\":\"微波炉清洗\",\"totalCount\":1,\"type\":0}],\"filterId\":200121968,\"selected\":false,\"title\":\"家电清洗\",\"totalCount\":39,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200123243,\"selected\":false,\"title\":\"全部\",\"totalCount\":7,\"type\":0}],\"filterId\":200122056,\"selected\":false,\"title\":\"墙面地面\",\"totalCount\":7,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200122889,\"selected\":false,\"title\":\"全部\",\"totalCount\":7,\"type\":0},{\"children\":[],\"filterId\":200122890,\"selected\":false,\"title\":\"空调\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122893,\"selected\":false,\"title\":\"电视机\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122892,\"selected\":false,\"title\":\"洗衣机\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122894,\"selected\":false,\"title\":\"热水器\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122899,\"selected\":false,\"title\":\"燃气灶\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122900,\"selected\":false,\"title\":\"油烟机\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200122891,\"selected\":false,\"title\":\"冰箱\",\"totalCount\":1,\"type\":0}],\"filterId\":200121969,\"selected\":false,\"title\":\"拆装移机\",\"totalCount\":14,\"type\":0},{\"children\":[{\"children\":[],\"filterId\":200123224,\"selected\":false,\"title\":\"全部\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123225,\"selected\":false,\"title\":\"防盗门\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123226,\"selected\":false,\"title\":\"室内门\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123227,\"selected\":false,\"title\":\"窗户\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123228,\"selected\":false,\"title\":\"纱窗\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123229,\"selected\":false,\"title\":\"换玻璃\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123230,\"selected\":false,\"title\":\"防护栏\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123231,\"selected\":false,\"title\":\"衣柜\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123232,\"selected\":false,\"title\":\"桌椅\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123233,\"selected\":false,\"title\":\"床\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123234,\"selected\":false,\"title\":\"鱼缸\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123235,\"selected\":false,\"title\":\"补漆贴膜\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123236,\"selected\":false,\"title\":\"沙发翻新\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123237,\"selected\":false,\"title\":\"沙发维修\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123238,\"selected\":false,\"title\":\"沙发保养\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123239,\"selected\":false,\"title\":\"合页\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123240,\"selected\":false,\"title\":\"抽屉\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123241,\"selected\":false,\"title\":\"把手/拉手\",\"totalCount\":1,\"type\":0},{\"children\":[],\"filterId\":200123242,\"selected\":false,\"title\":\"滑轮\",\"totalCount\":1,\"type\":0}],\"filterId\":200122055,\"selected\":false,\"title\":\"门窗家具\",\"totalCount\":19,\"type\":0}],\"id\":0,\"selected\":false,\"type\":0}";
        return JSON.parseObject(str,FilterM.class);
    }

    public static void main(String[] args) {
        File file = new File("/tmp/searchkeywords.txt");
        if (!file.exists()) {
            return;
        }
        FilterConfig.Config filterConfig = new FilterConfig.Config();
        Map<String, Map<String, FilterConfig.MultipleFilterConfig>> multipleFilterKeywords = new HashMap<>();
        Map<String, FilterConfig.MultipleFilterConfig> configMap = new HashMap();
        multipleFilterKeywords.put("团购", configMap);
        filterConfig.setMultipleFilterKeywords(multipleFilterKeywords);
        String filter_1 = null;
        String readLine = null;
        FilterConfig.MultipleFilterConfig topConfig = null;
        BufferedReader reader = null;
        StringBuffer sbf = new StringBuffer();
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                if (StringUtils.isEmpty(tempStr)) {
                    continue;
                }
                List<String> values = Lists.newArrayList(tempStr.split("\t"));
                if (StringUtils.isNotBlank(values.get(0))) {
                    topConfig = new FilterConfig.MultipleFilterConfig();
                    topConfig.setChild(new HashMap<>());
                    configMap.put(values.get(0), topConfig);
                }
                if (values.size() < 3) {
                    continue;
                }
                List<String> keyWords = Lists.newArrayList(values.get(2).split("、"));
                FilterConfig.MultipleFilterConfig keyWordsConfig = new FilterConfig.MultipleFilterConfig();
                keyWordsConfig.setKey(keyWords);
                topConfig.getChild().put(values.get(1), keyWordsConfig);
            }
            System.out.println(JsonCodec.encodeWithUTF8(filterConfig));
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }

    }
}
